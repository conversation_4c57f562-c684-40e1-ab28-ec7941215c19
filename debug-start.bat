@echo off
title 制药DMS调试启动
color 0A

echo ========================================
echo    制药DMS调试启动脚本
echo ========================================
echo.

cd /d D:\learn\java\dms

echo 当前目录: %CD%
echo.

echo 检查Java环境...
java -version
echo.

echo 检查JAR文件...
if exist "target\dms-1.0.0.jar" (
    echo ✅ JAR文件存在
    dir target\dms-1.0.0.jar
) else (
    echo ❌ JAR文件不存在
    pause
    exit /b 1
)
echo.

echo 启动PostgreSQL数据库...
D:\sql\pgsql\bin\pg_ctl -D D:\sql\pgsql\data start
echo.

echo 等待数据库启动...
timeout /t 5 /nobreak >nul
echo.

echo 测试数据库连接...
D:\sql\pgsql\bin\psql -U postgres -c "SELECT version();" >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✅ 数据库连接成功，使用PostgreSQL
    set PROFILE=postgresql
) else (
    echo ⚠️ 数据库连接失败，使用H2数据库
    set PROFILE=h2
)
echo.

echo 启动应用程序 (Profile: %PROFILE%)...
echo 命令: java -jar target\dms-1.0.0.jar --spring.profiles.active=%PROFILE% --server.address=0.0.0.0
echo.

REM 启动应用程序并显示输出
java -jar target\dms-1.0.0.jar ^
    --spring.profiles.active=%PROFILE% ^
    --server.address=0.0.0.0 ^
    --logging.level.com.pharma.dms=INFO ^
    --logging.level.org.springframework.boot=INFO

pause
