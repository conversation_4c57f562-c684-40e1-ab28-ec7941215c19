/**
 * 文档管理优化脚本
 * 专门处理文档上传、显示、搜索等功能的优化
 */

class DocumentManager {
    constructor() {
        this.currentPage = 0;
        this.pageSize = 10;
        this.isLoading = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        
        this.init();
    }

    init() {
        console.log('🚀 文档管理器初始化...');
        this.bindEvents();
        this.loadDocuments();
        this.loadCategories();
        this.loadUsers();
    }

    bindEvents() {
        // 搜索表单事件
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.currentPage = 0;
                this.loadDocuments();
            });
        }

        // 上传表单事件
        const uploadForm = document.getElementById('uploadDocumentForm');
        if (uploadForm) {
            uploadForm.addEventListener('submit', (e) => this.handleUpload(e));
        }

        // 清除搜索按钮
        const clearBtn = document.getElementById('clearSearchBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearSearch());
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshDocuments());
        }
    }

    async loadDocuments() {
        if (this.isLoading) {
            console.log('⏳ 正在加载中，跳过重复请求');
            return;
        }

        try {
            this.isLoading = true;
            this.showLoadingState();

            console.log('=== 加载文档列表 ===');
            console.log(`页码: ${this.currentPage}, 大小: ${this.pageSize}`);

            const searchParams = new URLSearchParams({
                page: this.currentPage,
                size: this.pageSize,
                sortBy: 'createdAt',
                sortDir: 'desc'
            });

            // 添加搜索条件
            this.addSearchParams(searchParams);

            const response = await authUtils.secureApiCall(`/dms/api/documents?${searchParams}`);

            if (response.ok) {
                const result = await response.json();
                console.log('✅ 文档加载成功:', result);
                
                this.displayDocuments(result.data.content);
                this.updatePagination(result.data);
                this.retryCount = 0; // 重置重试计数
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('❌ 加载文档失败:', error);
            this.handleLoadError(error);
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
        }
    }

    addSearchParams(searchParams) {
        const title = document.getElementById('searchTitle')?.value;
        const categoryId = document.getElementById('searchCategory')?.value;
        const status = document.getElementById('searchStatus')?.value;
        const ownerId = document.getElementById('searchOwner')?.value;

        if (title) searchParams.append('title', title);
        if (categoryId) searchParams.append('categoryId', categoryId);
        if (status) searchParams.append('status', status);
        if (ownerId) searchParams.append('ownerId', ownerId);
    }

    displayDocuments(documents) {
        console.log('=== 显示文档列表 ===');
        console.log('文档数量:', documents ? documents.length : 0);

        const tbody = document.getElementById('documentsTableBody');
        if (!tbody) {
            console.error('❌ 未找到文档表格体元素');
            return;
        }

        tbody.innerHTML = '';

        if (!documents || documents.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted py-5">
                        <i class="fas fa-folder-open fa-3x mb-3 text-secondary"></i>
                        <div class="h5">暂无文档</div>
                        <div class="text-muted">点击上传按钮添加第一个文档</div>
                    </td>
                </tr>
            `;
            return;
        }

        documents.forEach((doc, index) => {
            try {
                const row = this.createDocumentRow(doc, index);
                tbody.appendChild(row);
            } catch (error) {
                console.error(`❌ 创建文档行失败 (${index + 1}):`, error);
            }
        });

        console.log('✅ 文档列表显示完成');
    }

    createDocumentRow(doc, index) {
        const row = document.createElement('tr');
        row.className = 'document-row';
        row.setAttribute('data-document-id', doc.id);

        // 安全地获取文档属性
        const title = doc.title || '未命名文档';
        const originalFileName = doc.originalFileName || doc.fileName || '未知文件';
        const status = doc.status || 'DRAFT';
        const categoryName = doc.categoryName || doc.category?.name || '未分类';
        const ownerUsername = doc.ownerUsername || doc.owner?.username || '未知用户';
        const fileSize = doc.fileSize || 0;
        const versionNumber = doc.versionNumber || 1;
        const createdAt = doc.createdAt || new Date().toISOString();

        row.innerHTML = `
            <td>
                <div class="d-flex align-items-center">
                    <i class="fas fa-file-alt me-2 text-primary"></i>
                    <div>
                        <div class="fw-bold text-truncate" style="max-width: 200px;" title="${title}">${title}</div>
                        <small class="text-muted text-truncate d-block" style="max-width: 200px;" title="${originalFileName}">${originalFileName}</small>
                    </div>
                </div>
            </td>
            <td><span class="text-truncate d-inline-block" style="max-width: 120px;" title="${categoryName}">${categoryName}</span></td>
            <td><span class="text-truncate d-inline-block" style="max-width: 100px;" title="${ownerUsername}">${ownerUsername}</span></td>
            <td><span class="badge ${this.getStatusBadgeClass(status)}">${this.getStatusDisplayName(status)}</span></td>
            <td>
                <span class="badge bg-secondary">v${versionNumber}</span>
                ${doc.isCurrentVersion ? '<i class="fas fa-star text-warning ms-1" title="当前版本"></i>' : ''}
            </td>
            <td>${this.formatFileSize(fileSize)}</td>
            <td><small>${this.formatDate(createdAt)}</small></td>
            <td>
                <div class="btn-group btn-group-sm" role="group">
                    <button class="btn btn-outline-primary" onclick="documentManager.viewDocument(${doc.id})" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="documentManager.downloadDocument(${doc.id})" title="下载文档">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="documentManager.previewDocument(${doc.id})" title="预览文档">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="btn btn-outline-warning" onclick="documentManager.showVersions(${doc.id})" title="版本历史">
                        <i class="fas fa-history"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="documentManager.deleteDocument(${doc.id})" title="删除文档">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        return row;
    }

    async handleUpload(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const fileInput = document.getElementById('documentFile');
        
        if (!fileInput.files[0]) {
            this.showNotification('请选择要上传的文件', 'warning');
            return;
        }

        const file = fileInput.files[0];
        
        // 文件大小检查
        if (file.size > 50 * 1024 * 1024) {
            this.showNotification('文件大小不能超过50MB', 'error');
            return;
        }

        try {
            this.showUploadProgress(true);
            
            const response = await authUtils.secureApiCall('/dms/api/documents/upload', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                this.showNotification('文档上传成功！', 'success');
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('uploadDocumentModal'));
                if (modal) modal.hide();
                
                // 重置表单
                form.reset();
                
                // 延迟刷新列表
                setTimeout(() => this.refreshDocuments(), 500);
            } else {
                const errorText = await response.text();
                throw new Error(errorText);
            }
        } catch (error) {
            console.error('❌ 上传失败:', error);
            this.showNotification('上传失败: ' + error.message, 'error');
        } finally {
            this.showUploadProgress(false);
        }
    }

    // 工具方法
    getStatusBadgeClass(status) {
        const classes = {
            'DRAFT': 'bg-secondary',
            'UNDER_REVIEW': 'bg-warning',
            'APPROVED': 'bg-success',
            'PUBLISHED': 'bg-primary',
            'ARCHIVED': 'bg-dark'
        };
        return classes[status] || 'bg-secondary';
    }

    getStatusDisplayName(status) {
        const names = {
            'DRAFT': '草稿',
            'UNDER_REVIEW': '审核中',
            'APPROVED': '已批准',
            'PUBLISHED': '已发布',
            'ARCHIVED': '已归档'
        };
        return names[status] || status;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    formatDate(dateString) {
        if (!dateString) return '未知时间';
        try {
            return new Date(dateString).toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            return '无效日期';
        }
    }

    showNotification(message, type = 'info') {
        if (typeof UIEnhancements !== 'undefined') {
            UIEnhancements.showNotification(message, type);
        } else {
            alert(message);
        }
    }

    showLoadingState() {
        const tbody = document.getElementById('documentsTableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载文档...</div>
                    </td>
                </tr>
            `;
        }
    }

    hideLoadingState() {
        // 加载状态会被实际数据替换，这里不需要特别处理
    }

    showUploadProgress(show) {
        const submitBtn = document.querySelector('#uploadDocumentForm button[type="submit"]');
        if (submitBtn) {
            if (show) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>上传中...';
            } else {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-upload me-2"></i>上传文档';
            }
        }
    }

    handleLoadError(error) {
        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            console.log(`⚠️ 重试加载文档 (${this.retryCount}/${this.maxRetries})`);
            setTimeout(() => this.loadDocuments(), 1000 * this.retryCount);
        } else {
            const tbody = document.getElementById('documentsTableBody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center text-danger py-4">
                            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                            <div>加载失败: ${error.message}</div>
                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="documentManager.refreshDocuments()">
                                <i class="fas fa-redo me-1"></i>重试
                            </button>
                        </td>
                    </tr>
                `;
            }
        }
    }

    refreshDocuments() {
        this.retryCount = 0;
        this.loadDocuments();
    }

    clearSearch() {
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.reset();
            this.currentPage = 0;
            this.loadDocuments();
        }
    }

    // 占位方法，将在后续实现
    async loadCategories() {
        // TODO: 实现分类加载
    }

    async loadUsers() {
        // TODO: 实现用户加载
    }

    updatePagination(pageData) {
        // TODO: 实现分页更新
    }

    async viewDocument(id) {
        // TODO: 实现文档查看
    }

    async downloadDocument(id) {
        // TODO: 实现文档下载
    }

    async previewDocument(id) {
        // TODO: 实现文档预览
    }

    async showVersions(id) {
        // TODO: 实现版本历史
    }

    async deleteDocument(id) {
        // TODO: 实现文档删除
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.documentManager = new DocumentManager();
    console.log('✅ 文档管理器已初始化');
});
