<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle} + ' - 制药DMS系统'">开始培训 - 制药DMS系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }
        
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .training-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .progress-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .course-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3 fs-6" href="/dms/dashboard">制药DMS系统</a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <a class="nav-link px-3" href="#" onclick="logout()">退出</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/my-training">
                                <i class="fas fa-user-graduate me-2"></i>我的培训
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/training-courses">
                                <i class="fas fa-book me-2"></i>培训课程
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">开始培训</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button class="btn btn-outline-secondary" onclick="goBack()">
                            <i class="fas fa-arrow-left me-2"></i>返回
                        </button>
                    </div>
                </div>

                <!-- Course Information -->
                <div class="course-info" id="courseInfo">
                    <div class="row">
                        <div class="col-md-8">
                            <h3 id="courseTitle">加载中...</h3>
                            <p id="courseDescription" class="mb-2">课程描述加载中...</p>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-light text-dark me-2" id="courseType">类型</span>
                                <span class="badge bg-warning text-dark me-2" id="courseDuration">时长</span>
                                <span class="badge bg-info text-dark" id="courseLevel">难度</span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="mb-2">
                                <small>讲师</small>
                                <div id="instructorName" class="fw-bold">加载中...</div>
                            </div>
                            <div>
                                <small>版本</small>
                                <div id="courseVersion" class="fw-bold">v1.0</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Section -->
                <div class="progress-section">
                    <h5>培训进度</h5>
                    <div class="progress mb-3" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" style="width: 0%" id="trainingProgress">0%</div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-primary" id="completedSections">0</div>
                                <small class="text-muted">已完成章节</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-info" id="totalSections">0</div>
                                <small class="text-muted">总章节数</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-success" id="timeSpent">0分钟</div>
                                <small class="text-muted">学习时间</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-warning" id="currentScore">-</div>
                                <small class="text-muted">当前分数</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Training Content -->
                <div class="training-content">
                    <div id="trainingContentArea">
                        <div class="text-center py-5">
                            <i class="fas fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                            <h5>正在加载培训内容...</h5>
                            <p class="text-muted">请稍候，系统正在为您准备培训材料</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <button class="btn btn-outline-secondary" id="prevBtn" onclick="previousSection()" disabled>
                        <i class="fas fa-chevron-left me-2"></i>上一节
                    </button>
                    
                    <div class="btn-group">
                        <button class="btn btn-primary" id="startBtn" onclick="startTraining()">
                            <i class="fas fa-play me-2"></i>开始培训
                        </button>
                        <button class="btn btn-success d-none" id="continueBtn" onclick="continueTraining()">
                            <i class="fas fa-play me-2"></i>继续学习
                        </button>
                        <button class="btn btn-warning d-none" id="pauseBtn" onclick="pauseTraining()">
                            <i class="fas fa-pause me-2"></i>暂停
                        </button>
                    </div>
                    
                    <button class="btn btn-outline-primary" id="nextBtn" onclick="nextSection()" disabled>
                        下一节<i class="fas fa-chevron-right ms-2"></i>
                    </button>
                </div>

                <!-- Training Status -->
                <div class="alert alert-info d-none" id="trainingStatus">
                    <i class="fas fa-info-circle me-2"></i>
                    <span id="statusMessage">准备开始培训</span>
                </div>
            </main>
        </div>
    </div>

    <script>
        // 全局变量
        let courseId = null;
        let assignmentId = null;
        let currentSection = 0;
        let totalSections = 0;
        let trainingRecord = null;
        let startTime = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL或模板变量获取ID
            const urlParams = new URLSearchParams(window.location.search);
            courseId = urlParams.get('courseId') || document.querySelector('meta[name="courseId"]')?.content;
            assignmentId = urlParams.get('assignmentId') || document.querySelector('meta[name="assignmentId"]')?.content;
            
            if (courseId) {
                loadCourseInfo();
            } else if (assignmentId) {
                loadAssignmentInfo();
            } else {
                showError('无法获取课程信息');
            }
        });

        async function loadCourseInfo() {
            try {
                const response = await authUtils.secureApiCall(`/dms/api/training-courses/${courseId}`);
                if (response.ok) {
                    const result = await response.json();
                    displayCourseInfo(result.data);
                } else {
                    showError('加载课程信息失败');
                }
            } catch (error) {
                console.error('Error loading course info:', error);
                showError('加载课程信息失败');
            }
        }

        function displayCourseInfo(course) {
            document.getElementById('courseTitle').textContent = course.title;
            document.getElementById('courseDescription').textContent = course.description || '暂无描述';
            document.getElementById('courseType').textContent = course.courseType || '通用培训';
            document.getElementById('courseDuration').textContent = `${course.durationHours || 1}小时`;
            document.getElementById('courseLevel').textContent = course.difficultyLevel || '初级';
            document.getElementById('instructorName').textContent = 
                course.instructor ? `${course.instructor.firstName} ${course.instructor.lastName}` : '系统';
            document.getElementById('courseVersion').textContent = `v${course.version || '1.0'}`;
            
            // 加载真实章节数据
            loadCourseSections(course.id);
        }

        async function loadCourseSections(courseId) {
            try {
                console.log('加载课程章节数据:', courseId);
                const response = await authUtils.secureApiCall(`/dms/api/training-courses/${courseId}/sections`);

                if (response.ok) {
                    const result = await response.json();
                    const sections = result.data;

                    console.log('课程章节数据:', sections);

                    totalSections = sections.length || 1; // 至少有1个章节
                    document.getElementById('totalSections').textContent = totalSections;

                    console.log('✅ 课程章节数据更新完成，总章节数:', totalSections);
                } else {
                    console.warn('获取课程章节失败，使用默认值');
                    totalSections = 1; // 默认1个章节
                    document.getElementById('totalSections').textContent = totalSections;
                }
            } catch (error) {
                console.error('加载课程章节数据失败:', error);
                totalSections = 1; // 默认1个章节
                document.getElementById('totalSections').textContent = totalSections;
            }
        }

        function startTraining() {
            document.getElementById('startBtn').classList.add('d-none');
            document.getElementById('continueBtn').classList.remove('d-none');
            document.getElementById('pauseBtn').classList.remove('d-none');

            startTime = new Date();
            showStatus('培训已开始，祝您学习愉快！', 'success');

            // 开始第一节
            loadSection(1);
        }

        function continueTraining() {
            document.getElementById('continueBtn').classList.add('d-none');
            document.getElementById('pauseBtn').classList.remove('d-none');
            showStatus('继续学习...', 'info');
        }

        function pauseTraining() {
            document.getElementById('pauseBtn').classList.add('d-none');
            document.getElementById('continueBtn').classList.remove('d-none');
            showStatus('培训已暂停', 'warning');
        }

        function loadSection(sectionNumber) {
            currentSection = sectionNumber;
            
            // 更新进度
            const progress = (currentSection / totalSections) * 100;
            document.getElementById('trainingProgress').style.width = progress + '%';
            document.getElementById('trainingProgress').textContent = Math.round(progress) + '%';
            document.getElementById('completedSections').textContent = currentSection - 1;
            
            // 更新按钮状态
            document.getElementById('prevBtn').disabled = currentSection <= 1;
            document.getElementById('nextBtn').disabled = currentSection >= totalSections;
            
            // 加载章节内容
            const content = `
                <h4>第${sectionNumber}章：GMP基础知识</h4>
                <div class="mb-4">
                    <p>本章将介绍GMP（Good Manufacturing Practice）的基本概念和重要性。</p>
                    <h5>学习目标</h5>
                    <ul>
                        <li>理解GMP的定义和目的</li>
                        <li>掌握GMP的基本原则</li>
                        <li>了解GMP在制药行业的应用</li>
                    </ul>
                    
                    <h5>主要内容</h5>
                    <p>GMP是一套适用于制药、食品等行业的强制性标准，要求企业从原料、人员、设施设备、生产过程、包装运输、质量控制等方面按国家有关法规达到卫生质量要求。</p>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>重点提示：</strong>GMP不仅是技术标准，更是质量管理理念的体现。
                    </div>
                </div>
                
                <div class="text-center">
                    <button class="btn btn-success" onclick="completeSection()">
                        <i class="fas fa-check me-2"></i>完成本章学习
                    </button>
                </div>
            `;
            
            document.getElementById('trainingContentArea').innerHTML = content;
        }

        function completeSection() {
            if (currentSection < totalSections) {
                nextSection();
            } else {
                completeTraining();
            }
        }

        function nextSection() {
            if (currentSection < totalSections) {
                loadSection(currentSection + 1);
            }
        }

        function previousSection() {
            if (currentSection > 1) {
                loadSection(currentSection - 1);
            }
        }

        function completeTraining() {
            showStatus('恭喜！您已完成所有培训内容', 'success');
            document.getElementById('trainingContentArea').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-trophy fa-4x text-warning mb-3"></i>
                    <h3>培训完成！</h3>
                    <p class="text-muted mb-4">您已成功完成本次培训课程</p>
                    <button class="btn btn-primary me-2" onclick="takeExam()">
                        <i class="fas fa-clipboard-check me-2"></i>参加考试
                    </button>
                    <button class="btn btn-outline-secondary" onclick="goBack()">
                        <i class="fas fa-arrow-left me-2"></i>返回课程列表
                    </button>
                </div>
            `;
        }

        function takeExam() {
            alert('考试功能将在后续版本中实现');
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('trainingStatus');
            statusDiv.className = `alert alert-${type}`;
            document.getElementById('statusMessage').textContent = message;
            statusDiv.classList.remove('d-none');
            
            // 3秒后自动隐藏
            setTimeout(() => {
                statusDiv.classList.add('d-none');
            }, 3000);
        }

        function showError(message) {
            document.getElementById('trainingContentArea').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h5>加载失败</h5>
                    <p class="text-muted">${message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }

        function goBack() {
            window.history.back();
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 认证工具 -->
    <script src="/dms/js/auth.js"></script>
</body>
</html>
