#!/bin/bash

# 制药DMS系统 - 简化启动脚本
# 适用于Git Bash环境

echo "🚀 启动制药DMS系统..."

# 切换到应用目录
cd /d/learn/java/dms

# 确保PostgreSQL运行
echo "🗄️  确保PostgreSQL运行..."
/d/sql/pgsql/bin/pg_ctl -D /d/sql/pgsql/data start 2>/dev/null || echo "PostgreSQL已在运行"

# 等待数据库启动
sleep 3

# 启动应用程序
echo "🚀 启动应用程序..."
echo "访问地址: http://172.100.15.120:8081/dms/login"
echo "按 Ctrl+C 停止应用程序"
echo ""

# 使用Maven启动（正确的语法）
mvn spring-boot:run "-Dspring-boot.run.profiles=postgresql"
