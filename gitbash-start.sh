#!/bin/bash

# 制药DMS系统 - Git Bash 一键启动命令
# 使用方法: bash gitbash-start.sh

echo "=========================================="
echo "🏥 制药文档管理系统 (Pharmaceutical DMS)"
echo "🚀 Git Bash 一键启动"
echo "=========================================="

# 切换到正确目录
cd /d/learn/java/dms

# 显示当前目录
echo "📍 当前目录: $(pwd)"

# 检查PostgreSQL状态
echo "🗄️  检查PostgreSQL数据库..."
if ! tasklist.exe | grep -q postgres.exe; then
    echo "启动PostgreSQL..."
    /d/sql/pgsql/bin/pg_ctl -D /d/sql/pgsql/data start
    sleep 5
else
    echo "✅ PostgreSQL已在运行"
fi

# 停止可能存在的Java进程
echo "🔄 清理旧进程..."
taskkill.exe //F //IM java.exe 2>/dev/null || echo "没有需要停止的Java进程"

# 显示启动信息
echo ""
echo "🚀 启动DMS应用程序..."
echo "📊 配置信息:"
echo "   - 端口: 8081"
echo "   - 数据库: PostgreSQL"
echo "   - 本地访问: http://localhost:8081/dms/login"
echo "   - 网络访问: http://**************:8081/dms/login"
echo "   - 默认用户: admin / admin123"
echo ""
echo "⏳ 启动中，请等待..."
echo "按 Ctrl+C 停止应用程序"
echo ""

# 启动应用程序
mvn spring-boot:run "-Dspring-boot.run.profiles=postgresql"
