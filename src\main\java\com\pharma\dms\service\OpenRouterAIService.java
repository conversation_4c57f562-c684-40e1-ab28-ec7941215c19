package com.pharma.dms.service;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pharma.dms.config.AIConfig;

import reactor.core.publisher.Mono;

@Service
public class OpenRouterAIService {

    private static final Logger logger = LoggerFactory.getLogger(OpenRouterAIService.class);

    @Autowired
    private WebClient openRouterWebClient;

    @Autowired
    private AIConfig.OpenRouterProperties openRouterProperties;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 分析文档内容并提取关键信息
     */
    // 暂时移除缓存注解，避免AsyncCache问题
    // @Cacheable(value = "documentAnalysis", key = "#content.hashCode()")
    public Mono<DocumentAnalysisResult> analyzeDocument(String content, String documentType) {
        String prompt = buildDocumentAnalysisPrompt(content, documentType);
        
        return callOpenRouterAPI(prompt)
                .map(this::parseDocumentAnalysisResponse)
                .doOnError(error -> logger.error("Error analyzing document", error))
                .onErrorReturn(new DocumentAnalysisResult("分析失败", "未知", List.of(), "错误"));
    }

    /**
     * 检查文档的GMP合规性
     */
    // @Cacheable(value = "complianceCheck", key = "#content.hashCode()")
    public Mono<ComplianceCheckResult> checkCompliance(String content, String documentType) {
        String prompt = buildComplianceCheckPrompt(content, documentType);
        
        return callOpenRouterAPI(prompt)
                .map(this::parseComplianceCheckResponse)
                .doOnError(error -> logger.error("Error checking compliance", error))
                .onErrorReturn(new ComplianceCheckResult(false, List.of("合规性检查失败"), List.of(), 0));
    }

    /**
     * 自动分类文档
     */
    // @Cacheable(value = "documentCategorization", key = "#content.hashCode()")
    public Mono<DocumentCategorizationResult> categorizeDocument(String content, String fileName) {
        String prompt = buildCategorizationPrompt(content, fileName);
        
        return callOpenRouterAPI(prompt)
                .map(this::parseCategorizationResponse)
                .doOnError(error -> logger.error("Error categorizing document", error))
                .onErrorReturn(new DocumentCategorizationResult("未分类", List.of(), 0.0));
    }

    /**
     * 生成文档摘要
     */
    // @Cacheable(value = "documentSummary", key = "#content.hashCode()")
    public Mono<String> generateSummary(String content, int maxLength) {
        String prompt = buildSummaryPrompt(content, maxLength);

        return callOpenRouterAPI(prompt)
                .map(this::extractTextResponse)
                .doOnError(error -> logger.error("Error generating summary", error))
                .onErrorReturn("摘要生成失败");
    }

    /**
     * 根据文档内容生成培训大纲
     */
    // @Cacheable(value = "trainingOutline", key = "#content.hashCode() + #courseTitle.hashCode()")
    public Mono<String> generateTrainingOutline(String content, String courseTitle) {
        String prompt = buildTrainingOutlinePrompt(content, courseTitle);

        return callOpenRouterAPI(prompt)
                .map(this::extractTextResponse)
                .doOnError(error -> logger.error("Error generating training outline", error))
                .onErrorReturn("培训大纲生成失败");
    }

    /**
     * 生成培训内容详情
     */
    // @Cacheable(value = "trainingContent", key = "#outline.hashCode() + #courseTitle.hashCode()")
    public Mono<String> generateTrainingContent(String outline, String courseTitle) {
        String prompt = buildTrainingContentPrompt(outline, courseTitle);

        return callOpenRouterAPI(prompt)
                .map(this::extractTextResponse)
                .doOnError(error -> logger.error("Error generating training content", error))
                .onErrorReturn("培训内容生成失败");
    }

    /**
     * 生成考试题目
     */
    // @Cacheable(value = "examQuestions", key = "#courseContent.hashCode() + #questionCount")
    public Mono<String> generateExamQuestions(String courseContent, String courseTitle, int questionCount) {
        String prompt = buildExamQuestionsPrompt(courseContent, courseTitle, questionCount);

        return callOpenRouterAPI(prompt)
                .map(this::extractTextResponse)
                .doOnError(error -> logger.error("Error generating exam questions", error))
                .onErrorReturn("考试题目生成失败");
    }

    /**
     * 调用OpenRouter API
     */
    private Mono<String> callOpenRouterAPI(String prompt) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", openRouterProperties.getModel());
        requestBody.put("messages", List.of(
                Map.of("role", "system", "content", "你是一个专业的制药行业文档分析专家，精通GMP规范和制药行业标准。请用中文回答。"),
                Map.of("role", "user", "content", prompt)
        ));
        requestBody.put("max_tokens", openRouterProperties.getMaxTokens());
        requestBody.put("temperature", openRouterProperties.getTemperature());

        return openRouterWebClient
                .post()
                .uri("/chat/completions")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofMillis(openRouterProperties.getTimeout()))
                .doOnNext(response -> logger.debug("OpenRouter API response: {}", response));
    }

    private String buildDocumentAnalysisPrompt(String content, String documentType) {
        return String.format("""
                请分析以下%s文档内容，提取关键信息：
                
                文档内容：
                %s
                
                请提供以下信息（JSON格式）：
                {
                    "summary": "文档摘要",
                    "category": "文档类别",
                    "keywords": ["关键词1", "关键词2"],
                    "riskLevel": "风险等级（低/中/高）"
                }
                """, documentType, content.length() > 3000 ? content.substring(0, 3000) + "..." : content);
    }

    private String buildComplianceCheckPrompt(String content, String documentType) {
        return String.format("""
                请检查以下%s文档是否符合GMP规范要求：
                
                文档内容：
                %s
                
                请提供合规性检查结果（JSON格式）：
                {
                    "isCompliant": true/false,
                    "violations": ["违规项1", "违规项2"],
                    "recommendations": ["建议1", "建议2"],
                    "complianceScore": 85
                }
                """, documentType, content.length() > 3000 ? content.substring(0, 3000) + "..." : content);
    }

    private String buildCategorizationPrompt(String content, String fileName) {
        return String.format("""
                请根据文档内容和文件名对文档进行分类：
                
                文件名：%s
                文档内容：
                %s
                
                可选类别：SOP（标准操作程序）、QM（质量手册）、VD（验证文档）、TM（培训材料）、RS（法规提交）、TD（技术文档）、FT（表格模板）、PP（政策程序）
                
                请提供分类结果（JSON格式）：
                {
                    "category": "类别代码",
                    "tags": ["标签1", "标签2"],
                    "confidence": 0.95
                }
                """, fileName, content.length() > 2000 ? content.substring(0, 2000) + "..." : content);
    }

    private String buildSummaryPrompt(String content, int maxLength) {
        return String.format("""
                请为以下文档生成一个不超过%d字的中文摘要：

                文档内容：
                %s

                摘要要求：
                1. 突出文档的主要内容和目的
                2. 包含关键的技术要点
                3. 简洁明了，便于快速理解
                """, maxLength, content.length() > 4000 ? content.substring(0, 4000) + "..." : content);
    }

    private String buildTrainingOutlinePrompt(String content, String courseTitle) {
        return String.format("""
                作为一名专业的制药行业培训专家，请根据以下文档内容为培训课程"%s"生成详细的培训大纲。

                文档内容：
                %s

                请生成一个结构化的培训大纲，包括：
                1. 课程目标（3-5个具体目标）
                2. 主要章节（5-8个章节，每个章节包含2-4个子主题）
                3. 重点知识点
                4. 实践环节建议
                5. 预期学习成果

                请确保内容符合GMP（良好生产规范）要求，并且适合制药行业从业人员学习。
                输出格式请使用Markdown格式。
                """, courseTitle, content.length() > 3000 ? content.substring(0, 3000) + "..." : content);
    }

    private String buildTrainingContentPrompt(String outline, String courseTitle) {
        return String.format("""
                作为制药行业培训专家，请根据以下培训大纲为课程"%s"生成详细的培训内容。

                培训大纲：
                %s

                请为每个章节生成详细内容，包括：
                1. 理论知识讲解
                2. 实际案例分析
                3. 操作步骤说明
                4. 注意事项和风险点
                5. 相关法规要求
                6. 最佳实践建议

                内容应该：
                - 专业且易于理解
                - 符合制药行业标准
                - 包含实际工作场景
                - 强调GMP合规性

                请使用Markdown格式输出，确保内容结构清晰。
                """, courseTitle, outline.length() > 3000 ? outline.substring(0, 3000) + "..." : outline);
    }

    private String buildExamQuestionsPrompt(String courseContent, String courseTitle, int questionCount) {
        return String.format("""
                作为制药行业培训评估专家，请根据以下培训内容为课程"%s"生成%d道考试题目。

                培训内容：
                %s

                请生成以下类型的题目：
                1. 单选题（占60%%）- 每题4个选项，标明正确答案和解析
                2. 多选题（占25%%）- 每题5个选项，可能有2-3个正确答案
                3. 判断题（占15%%）- 对错判断，提供详细解析

                题目要求：
                - 覆盖课程的核心知识点
                - 难度适中，符合实际工作需要
                - 重点考查GMP合规性理解
                - 包含实际工作场景题目
                - 每题都要有详细的答案解析

                输出格式：
                ```json
                {
                  "questions": [
                    {
                      "type": "single_choice",
                      "question": "题目内容",
                      "options": ["A. 选项1", "B. 选项2", "C. 选项3", "D. 选项4"],
                      "correct_answer": "A",
                      "explanation": "答案解析"
                    }
                  ]
                }
                ```
                """, courseTitle, questionCount, courseContent.length() > 2000 ? courseContent.substring(0, 2000) + "..." : courseContent);
    }

    private DocumentAnalysisResult parseDocumentAnalysisResponse(String response) {
        try {
            JsonNode root = objectMapper.readTree(response);
            JsonNode content = root.path("choices").get(0).path("message").path("content");
            JsonNode analysis = objectMapper.readTree(content.asText());
            
            return new DocumentAnalysisResult(
                    analysis.path("summary").asText(),
                    analysis.path("category").asText(),
                    objectMapper.convertValue(analysis.path("keywords"), List.class),
                    analysis.path("riskLevel").asText()
            );
        } catch (Exception e) {
            logger.error("Error parsing document analysis response", e);
            return new DocumentAnalysisResult("解析失败", "未知", List.of(), "未知");
        }
    }

    private ComplianceCheckResult parseComplianceCheckResponse(String response) {
        try {
            JsonNode root = objectMapper.readTree(response);
            JsonNode content = root.path("choices").get(0).path("message").path("content");
            JsonNode compliance = objectMapper.readTree(content.asText());
            
            return new ComplianceCheckResult(
                    compliance.path("isCompliant").asBoolean(),
                    objectMapper.convertValue(compliance.path("violations"), List.class),
                    objectMapper.convertValue(compliance.path("recommendations"), List.class),
                    compliance.path("complianceScore").asInt()
            );
        } catch (Exception e) {
            logger.error("Error parsing compliance check response", e);
            return new ComplianceCheckResult(false, List.of("解析失败"), List.of(), 0);
        }
    }

    private DocumentCategorizationResult parseCategorizationResponse(String response) {
        try {
            JsonNode root = objectMapper.readTree(response);
            JsonNode content = root.path("choices").get(0).path("message").path("content");
            JsonNode categorization = objectMapper.readTree(content.asText());
            
            return new DocumentCategorizationResult(
                    categorization.path("category").asText(),
                    objectMapper.convertValue(categorization.path("tags"), List.class),
                    categorization.path("confidence").asDouble()
            );
        } catch (Exception e) {
            logger.error("Error parsing categorization response", e);
            return new DocumentCategorizationResult("未分类", List.of(), 0.0);
        }
    }

    private String extractTextResponse(String response) {
        try {
            JsonNode root = objectMapper.readTree(response);
            return root.path("choices").get(0).path("message").path("content").asText();
        } catch (Exception e) {
            logger.error("Error extracting text response", e);
            return "响应解析失败";
        }
    }

    // Result classes
    public static class DocumentAnalysisResult {
        private final String summary;
        private final String category;
        private final List<String> keywords;
        private final String riskLevel;

        public DocumentAnalysisResult(String summary, String category, List<String> keywords, String riskLevel) {
            this.summary = summary;
            this.category = category;
            this.keywords = keywords;
            this.riskLevel = riskLevel;
        }

        // Getters
        public String getSummary() { return summary; }
        public String getCategory() { return category; }
        public List<String> getKeywords() { return keywords; }
        public String getRiskLevel() { return riskLevel; }
    }

    public static class ComplianceCheckResult {
        private final boolean isCompliant;
        private final List<String> violations;
        private final List<String> recommendations;
        private final int complianceScore;

        public ComplianceCheckResult(boolean isCompliant, List<String> violations, List<String> recommendations, int complianceScore) {
            this.isCompliant = isCompliant;
            this.violations = violations;
            this.recommendations = recommendations;
            this.complianceScore = complianceScore;
        }

        // Getters
        public boolean isCompliant() { return isCompliant; }
        public List<String> getViolations() { return violations; }
        public List<String> getRecommendations() { return recommendations; }
        public int getComplianceScore() { return complianceScore; }
    }

    public static class DocumentCategorizationResult {
        private final String category;
        private final List<String> tags;
        private final double confidence;

        public DocumentCategorizationResult(String category, List<String> tags, double confidence) {
            this.category = category;
            this.tags = tags;
            this.confidence = confidence;
        }

        // Getters
        public String getCategory() { return category; }
        public List<String> getTags() { return tags; }
        public double getConfidence() { return confidence; }
    }
}
