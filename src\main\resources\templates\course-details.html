<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle} + ' - 制药DMS系统'">课程详情 - 制药DMS系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }
        
        .course-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .info-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .stat-card {
            text-align: center;
            padding: 1rem;
            border-radius: 8px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3 fs-6" href="/dms/dashboard">制药DMS系统</a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <a class="nav-link px-3" href="#" onclick="logout()">退出</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/my-training">
                                <i class="fas fa-user-graduate me-2"></i>我的培训
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/training-courses">
                                <i class="fas fa-book me-2"></i>培训课程
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">课程详情</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button class="btn btn-outline-secondary me-2" onclick="goBack()">
                            <i class="fas fa-arrow-left me-2"></i>返回
                        </button>
                        <button class="btn btn-success" id="startCourseBtn" onclick="startCourse()">
                            <i class="fas fa-play me-2"></i>开始学习
                        </button>
                    </div>
                </div>

                <!-- Course Header -->
                <div class="course-header" id="courseHeader">
                    <div class="row">
                        <div class="col-md-8">
                            <h2 id="courseTitle">加载中...</h2>
                            <p id="courseDescription" class="mb-3">课程描述加载中...</p>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-light text-dark me-2" id="courseType">类型</span>
                                <span class="badge bg-warning text-dark me-2" id="courseDuration">时长</span>
                                <span class="badge bg-info text-dark me-2" id="courseLevel">难度</span>
                                <span class="badge bg-success text-dark" id="courseStatus">状态</span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="mb-2">
                                <small>讲师</small>
                                <div id="instructorName" class="h5">加载中...</div>
                            </div>
                            <div class="mb-2">
                                <small>版本</small>
                                <div id="courseVersion" class="fw-bold">v1.0</div>
                            </div>
                            <div>
                                <small>创建时间</small>
                                <div id="createdAt" class="fw-bold">-</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Course Information -->
                    <div class="col-md-8">
                        <!-- Course Overview -->
                        <div class="info-card">
                            <h5><i class="fas fa-info-circle me-2"></i>课程概述</h5>
                            <div id="courseOverview">
                                <p>本课程将详细介绍GMP（Good Manufacturing Practice）的基本概念、实施要求和在制药行业的应用。</p>
                                
                                <h6>学习目标</h6>
                                <ul>
                                    <li>理解GMP的定义、目的和重要性</li>
                                    <li>掌握GMP的基本原则和核心要求</li>
                                    <li>了解GMP在制药生产中的具体应用</li>
                                    <li>学会识别和预防常见的GMP违规行为</li>
                                </ul>
                                
                                <h6>适用人员</h6>
                                <p>制药企业生产、质量、技术等相关岗位人员</p>
                            </div>
                        </div>

                        <!-- Course Content -->
                        <div class="info-card">
                            <h5><i class="fas fa-list me-2"></i>课程内容</h5>
                            <div id="courseContent">
                                <div class="accordion" id="contentAccordion">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#chapter1">
                                                第1章：GMP基础概念
                                            </button>
                                        </h2>
                                        <div id="chapter1" class="accordion-collapse collapse show" data-bs-parent="#contentAccordion">
                                            <div class="accordion-body">
                                                <ul>
                                                    <li>GMP的定义和发展历程</li>
                                                    <li>GMP的法律地位和重要性</li>
                                                    <li>GMP与其他质量管理体系的关系</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chapter2">
                                                第2章：GMP基本原则
                                            </button>
                                        </h2>
                                        <div id="chapter2" class="accordion-collapse collapse" data-bs-parent="#contentAccordion">
                                            <div class="accordion-body">
                                                <ul>
                                                    <li>质量管理体系</li>
                                                    <li>人员管理要求</li>
                                                    <li>厂房设施要求</li>
                                                    <li>设备管理要求</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chapter3">
                                                第3章：生产管理
                                            </button>
                                        </h2>
                                        <div id="chapter3" class="accordion-collapse collapse" data-bs-parent="#contentAccordion">
                                            <div class="accordion-body">
                                                <ul>
                                                    <li>生产管理的基本要求</li>
                                                    <li>批生产记录管理</li>
                                                    <li>物料管理</li>
                                                    <li>生产过程控制</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chapter4">
                                                第4章：质量控制
                                            </button>
                                        </h2>
                                        <div id="chapter4" class="accordion-collapse collapse" data-bs-parent="#contentAccordion">
                                            <div class="accordion-body">
                                                <ul>
                                                    <li>质量控制实验室管理</li>
                                                    <li>检验方法验证</li>
                                                    <li>稳定性研究</li>
                                                    <li>偏差和CAPA管理</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chapter5">
                                                第5章：案例分析与考核
                                            </button>
                                        </h2>
                                        <div id="chapter5" class="accordion-collapse collapse" data-bs-parent="#contentAccordion">
                                            <div class="accordion-body">
                                                <ul>
                                                    <li>典型GMP违规案例分析</li>
                                                    <li>预防措施和改进建议</li>
                                                    <li>综合练习</li>
                                                    <li>课程考核</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Course Statistics -->
                    <div class="col-md-4">
                        <div class="info-card">
                            <h5><i class="fas fa-chart-bar me-2"></i>课程统计</h5>
                            <div class="row">
                                <div class="col-6">
                                    <div class="stat-card">
                                        <div class="h4 text-primary" id="enrolledCount">0</div>
                                        <small class="text-muted">已报名</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-card">
                                        <div class="h4 text-success" id="completedCount">0</div>
                                        <small class="text-muted">已完成</small>
                                    </div>
                                </div>
                                <div class="col-6 mt-2">
                                    <div class="stat-card">
                                        <div class="h4 text-warning" id="averageScore">0</div>
                                        <small class="text-muted">平均分</small>
                                    </div>
                                </div>
                                <div class="col-6 mt-2">
                                    <div class="stat-card">
                                        <div class="h4 text-info" id="passRate">0%</div>
                                        <small class="text-muted">通过率</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Requirements -->
                        <div class="info-card">
                            <h5><i class="fas fa-clipboard-list me-2"></i>学习要求</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>完成所有章节学习</li>
                                <li><i class="fas fa-check text-success me-2"></i>参加在线考试</li>
                                <li><i class="fas fa-check text-success me-2"></i>考试成绩≥80分</li>
                                <li><i class="fas fa-check text-success me-2"></i>学习时长≥2小时</li>
                            </ul>
                        </div>

                        <!-- Related Documents -->
                        <div class="info-card">
                            <h5><i class="fas fa-file-alt me-2"></i>相关文档</h5>
                            <div class="list-group list-group-flush">
                                <a href="#" class="list-group-item list-group-item-action">
                                    <i class="fas fa-download me-2"></i>GMP培训手册.pdf
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <i class="fas fa-download me-2"></i>GMP检查清单.xlsx
                                </a>
                                <a href="#" class="list-group-item list-group-item-action">
                                    <i class="fas fa-download me-2"></i>案例分析资料.docx
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        let courseId = null;

        document.addEventListener('DOMContentLoaded', function() {
            // 从URL获取课程ID
            const pathParts = window.location.pathname.split('/');
            courseId = pathParts[pathParts.indexOf('course') + 1];
            
            if (courseId) {
                loadCourseDetails();
            } else {
                showError('无法获取课程ID');
            }
        });

        async function loadCourseDetails() {
            try {
                const response = await authUtils.secureApiCall(`/dms/api/training-courses/${courseId}`);
                if (response.ok) {
                    const result = await response.json();
                    displayCourseDetails(result.data);
                } else {
                    showError('加载课程详情失败');
                }
            } catch (error) {
                console.error('Error loading course details:', error);
                showError('加载课程详情失败');
            }
        }

        function displayCourseDetails(course) {
            document.getElementById('courseTitle').textContent = course.title;
            document.getElementById('courseDescription').textContent = course.description || '暂无描述';
            document.getElementById('courseType').textContent = course.courseType || '通用培训';
            document.getElementById('courseDuration').textContent = `${course.durationHours || 2}小时`;
            document.getElementById('courseLevel').textContent = course.difficultyLevel || '初级';
            document.getElementById('courseStatus').textContent = course.status || 'ACTIVE';
            document.getElementById('instructorName').textContent = 
                course.instructor ? `${course.instructor.firstName} ${course.instructor.lastName}` : '系统';
            document.getElementById('courseVersion').textContent = `v${course.version || '1.0'}`;
            document.getElementById('createdAt').textContent = 
                course.createdAt ? new Date(course.createdAt).toLocaleDateString() : '-';
            
            // 加载真实统计数据
            loadCourseStatistics(course.id);
        }

        async function loadCourseStatistics(courseId) {
            try {
                console.log('加载课程统计数据:', courseId);
                const response = await authUtils.secureApiCall(`/dms/api/training-courses/${courseId}/statistics`);

                if (response.ok) {
                    const result = await response.json();
                    const stats = result.data;

                    console.log('课程统计数据:', stats);

                    // 显示真实统计数据
                    document.getElementById('enrolledCount').textContent = stats.enrolledCount || '0';
                    document.getElementById('completedCount').textContent = stats.completedCount || '0';
                    document.getElementById('averageScore').textContent = stats.averageScore ? stats.averageScore.toFixed(1) : '0.0';
                    document.getElementById('passRate').textContent = stats.passRate ? stats.passRate.toFixed(1) + '%' : '0.0%';

                    console.log('✅ 课程统计数据更新完成');
                } else {
                    console.warn('获取课程统计失败，使用默认值');
                    // 使用默认值而不是随机数
                    document.getElementById('enrolledCount').textContent = '0';
                    document.getElementById('completedCount').textContent = '0';
                    document.getElementById('averageScore').textContent = '0.0';
                    document.getElementById('passRate').textContent = '0.0%';
                }
            } catch (error) {
                console.error('加载课程统计数据失败:', error);
                // 使用默认值而不是随机数
                document.getElementById('enrolledCount').textContent = '0';
                document.getElementById('completedCount').textContent = '0';
                document.getElementById('averageScore').textContent = '0.0';
                document.getElementById('passRate').textContent = '0.0%';
            }
        }

        function startCourse() {
            if (courseId) {
                window.location.href = `/dms/training/course/${courseId}/start`;
            }
        }

        function showError(message) {
            document.getElementById('courseHeader').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h5>加载失败</h5>
                    <p class="text-muted">${message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }

        function goBack() {
            window.history.back();
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 认证工具 -->
    <script src="/dms/js/auth.js"></script>
</body>
</html>
