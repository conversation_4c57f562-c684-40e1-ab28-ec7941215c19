package com.pharma.dms.controller;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.Document;
import com.pharma.dms.entity.DocumentPermission;
import com.pharma.dms.entity.User;
import com.pharma.dms.repository.DocumentRepository;
import com.pharma.dms.security.UserPrincipal;
import com.pharma.dms.service.DocumentService;
import com.pharma.dms.service.UserService;

@RestController
@RequestMapping("/api/documents")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DocumentController {

    @Autowired
    private DocumentService documentService;

    @Autowired
    private UserService userService;

    @Autowired
    private DocumentRepository documentRepository;

    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAllDocuments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        try {
            System.out.println("=== 获取文档列表 ===");
            System.out.println("页码: " + page + ", 大小: " + size);

            Sort sort = sortDir.equalsIgnoreCase("desc") ?
                       Sort.by(sortBy).descending() :
                       Sort.by(sortBy).ascending();

            Pageable pageable = PageRequest.of(page, size, sort);
            System.out.println("调用documentService.getAllDocuments...");

            Page<Document> documents = documentService.getAllDocuments(pageable);
            System.out.println("文档数量: " + documents.getTotalElements());

            // 创建优化的响应，确保数据完整性
            Map<String, Object> response = new HashMap<>();
            response.put("content", documents.getContent().stream().map(doc -> {
                Map<String, Object> simpleDoc = new HashMap<>();
                simpleDoc.put("id", doc.getId());
                simpleDoc.put("title", doc.getTitle() != null ? doc.getTitle() : "未命名文档");
                simpleDoc.put("description", doc.getDescription() != null ? doc.getDescription() : "");
                simpleDoc.put("fileName", doc.getFileName() != null ? doc.getFileName() : "");
                simpleDoc.put("originalFileName", doc.getOriginalFileName() != null ? doc.getOriginalFileName() : "");
                simpleDoc.put("fileSize", doc.getFileSize() != null ? doc.getFileSize() : 0L);
                simpleDoc.put("mimeType", doc.getMimeType() != null ? doc.getMimeType() : "application/octet-stream");
                simpleDoc.put("fileExtension", doc.getFileExtension() != null ? doc.getFileExtension() : "");
                simpleDoc.put("status", doc.getStatus() != null ? doc.getStatus().toString() : "DRAFT");
                simpleDoc.put("classification", doc.getClassification() != null ? doc.getClassification().toString() : "INTERNAL");
                simpleDoc.put("versionNumber", doc.getVersionNumber() != null ? doc.getVersionNumber() : 1);
                simpleDoc.put("versionLabel", doc.getVersionLabel() != null ? doc.getVersionLabel() : "v1.0");
                simpleDoc.put("isCurrentVersion", doc.getIsCurrentVersion() != null ? doc.getIsCurrentVersion() : true);
                simpleDoc.put("downloadCount", doc.getDownloadCount() != null ? doc.getDownloadCount() : 0L);
                simpleDoc.put("viewCount", doc.getViewCount() != null ? doc.getViewCount() : 0L);
                simpleDoc.put("createdAt", doc.getCreatedAt() != null ? doc.getCreatedAt().toString() : "");
                simpleDoc.put("updatedAt", doc.getUpdatedAt() != null ? doc.getUpdatedAt().toString() : "");

                // 安全地添加关联对象信息
                try {
                    if (doc.getCategory() != null) {
                        Map<String, Object> category = new HashMap<>();
                        category.put("id", doc.getCategory().getId());
                        category.put("name", doc.getCategory().getName());
                        simpleDoc.put("category", category);
                        simpleDoc.put("categoryName", doc.getCategory().getName());
                    } else {
                        simpleDoc.put("category", null);
                        simpleDoc.put("categoryName", "未分类");
                    }
                } catch (Exception e) {
                    System.err.println("获取文档分类失败: " + e.getMessage());
                    simpleDoc.put("category", null);
                    simpleDoc.put("categoryName", "未知分类");
                }

                try {
                    if (doc.getOwner() != null) {
                        Map<String, Object> owner = new HashMap<>();
                        owner.put("id", doc.getOwner().getId());
                        owner.put("username", doc.getOwner().getUsername());
                        owner.put("firstName", doc.getOwner().getFirstName());
                        owner.put("lastName", doc.getOwner().getLastName());
                        simpleDoc.put("owner", owner);
                        simpleDoc.put("ownerUsername", doc.getOwner().getUsername());
                    } else {
                        simpleDoc.put("owner", null);
                        simpleDoc.put("ownerUsername", "未知用户");
                    }
                } catch (Exception e) {
                    System.err.println("获取文档所有者失败: " + e.getMessage());
                    simpleDoc.put("owner", null);
                    simpleDoc.put("ownerUsername", "未知用户");
                }

                return simpleDoc;
            }).toList());

            response.put("pageable", Map.of(
                "pageNumber", documents.getNumber(),
                "pageSize", documents.getSize(),
                "sort", Map.of(
                    "sorted", documents.getSort().isSorted(),
                    "unsorted", documents.getSort().isUnsorted()
                )
            ));

            response.put("totalElements", documents.getTotalElements());
            response.put("totalPages", documents.getTotalPages());
            response.put("last", documents.isLast());
            response.put("first", documents.isFirst());
            response.put("numberOfElements", documents.getNumberOfElements());
            response.put("size", documents.getSize());
            response.put("number", documents.getNumber());
            response.put("empty", documents.isEmpty());

            return ResponseEntity.ok(ApiResponse.success("Documents retrieved successfully", response));
        } catch (Exception e) {
            System.out.println("获取文档列表失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to retrieve documents", e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Document>> getDocumentById(@PathVariable Long id,
                                                               @AuthenticationPrincipal UserPrincipal userPrincipal) {
        Optional<Document> document = documentService.getDocumentById(id);
        
        if (document.isPresent()) {
            User user = userService.getUserByUsername(userPrincipal.getUsername()).orElse(null);
            if (user != null && documentService.hasPermission(document.get(), user, DocumentPermission.PermissionType.READ)) {
                // Increment view count
                documentService.incrementViewCount(id);
                return ResponseEntity.ok(ApiResponse.success("Document found", document.get()));
            } else {
                return ResponseEntity.status(403)
                        .body(ApiResponse.error("Access denied", "Insufficient permissions"));
            }
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping("/upload")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> uploadDocument(
            @RequestParam("file") MultipartFile file,
            @RequestParam("title") String title,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "categoryId", required = false) Long categoryId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {

        try {
            System.out.println("=== 文档上传控制器 ===");
            System.out.println("文件名: " + file.getOriginalFilename());
            System.out.println("文件大小: " + file.getSize());
            System.out.println("标题: " + title);
            System.out.println("用户: " + userPrincipal.getUsername());

            User owner = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            System.out.println("用户找到: " + owner.getUsername());
            System.out.println("开始简化文档上传...");

            // 简化的文档上传逻辑，避免复杂的DocumentService
            try {
                // 创建上传目录
                String uploadDir = "uploads";
                java.nio.file.Path uploadPath = java.nio.file.Paths.get(uploadDir);
                if (!java.nio.file.Files.exists(uploadPath)) {
                    java.nio.file.Files.createDirectories(uploadPath);
                }

                // 生成唯一文件名
                String originalFileName = file.getOriginalFilename();
                String fileExtension = "";
                if (originalFileName != null && originalFileName.lastIndexOf('.') != -1) {
                    fileExtension = originalFileName.substring(originalFileName.lastIndexOf('.') + 1).toLowerCase();
                }
                String uniqueFileName = java.util.UUID.randomUUID().toString() + "." + fileExtension;
                java.nio.file.Path filePath = uploadPath.resolve(uniqueFileName);

                // 保存文件
                java.nio.file.Files.copy(file.getInputStream(), filePath, java.nio.file.StandardCopyOption.REPLACE_EXISTING);

                // 创建文档实体
                com.pharma.dms.entity.Document document = new com.pharma.dms.entity.Document();
                document.setTitle(title);
                document.setDescription(description);
                document.setFileName(uniqueFileName);
                document.setOriginalFileName(originalFileName);
                document.setFilePath(filePath.toString());
                document.setFileSize(file.getSize());
                document.setMimeType(file.getContentType());
                document.setFileExtension(fileExtension);
                document.setOwner(owner);
                document.setStatus(com.pharma.dms.entity.Document.DocumentStatus.DRAFT);
                document.setClassification(com.pharma.dms.entity.Document.DocumentClassification.INTERNAL);

                // 保存到数据库
                com.pharma.dms.entity.Document savedDocument = documentRepository.save(document);
                System.out.println("文档上传成功: " + savedDocument.getId());

                // 创建简化响应
                Map<String, Object> response = new HashMap<>();
                response.put("id", savedDocument.getId());
                response.put("title", savedDocument.getTitle());
                response.put("fileName", savedDocument.getFileName());
                response.put("originalFileName", savedDocument.getOriginalFileName());
                response.put("fileSize", savedDocument.getFileSize());
                response.put("status", savedDocument.getStatus().toString());
                response.put("ownerUsername", owner.getUsername());

                return ResponseEntity.ok(ApiResponse.success("Document uploaded successfully", response));
            } catch (Exception e) {
                System.out.println("简化上传失败: " + e.getMessage());
                e.printStackTrace();
                throw new RuntimeException("Simplified upload failed", e);
            }
        } catch (RuntimeException e) {
            System.out.println("文档上传运行时异常: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to upload document", e.getMessage()));
        } catch (Exception e) {
            System.out.println("文档上传未知异常: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to upload document", e.getMessage()));
        }
    }



    @PostMapping("/{id}/new-version")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Document>> createNewVersion(
            @PathVariable Long id,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "versionLabel", required = false) String versionLabel,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        try {
            User user = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            Document newVersion = documentService.createNewVersion(id, file, versionLabel, user);
            return ResponseEntity.ok(ApiResponse.success("New version created successfully", newVersion));
        } catch (IOException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to create new version", e.getMessage()));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to create new version", e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Void>> deleteDocument(@PathVariable Long id,
                                                           @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            User user = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            documentService.deleteDocument(id, user);
            return ResponseEntity.ok(ApiResponse.success("Document deleted successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to delete document", e.getMessage()));
        }
    }

    @GetMapping("/{id}/download")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<Resource> downloadDocument(@PathVariable Long id,
                                                    @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 文档下载开始 ===");
            System.out.println("文档ID: " + id);
            System.out.println("用户: " + userPrincipal.getUsername());

            Optional<Document> documentOpt = documentService.getDocumentById(id);
            if (documentOpt.isEmpty()) {
                System.out.println("❌ 文档不存在: " + id);
                return ResponseEntity.notFound().build();
            }

            Document document = documentOpt.get();
            System.out.println("✅ 找到文档: " + document.getTitle());
            System.out.println("文件路径: " + document.getFilePath());

            // 简化权限检查，暂时跳过
            System.out.println("跳过权限检查");

            Path filePath = Paths.get(document.getFilePath());
            System.out.println("完整文件路径: " + filePath.toAbsolutePath());

            Resource resource = new UrlResource(filePath.toUri());
            System.out.println("资源存在: " + resource.exists());
            System.out.println("资源可读: " + resource.isReadable());

            if (resource.exists() && resource.isReadable()) {
                System.out.println("✅ 文件可以下载");

                // Increment download count
                try {
                    documentService.incrementDownloadCount(id);
                } catch (Exception e) {
                    System.out.println("更新下载计数失败: " + e.getMessage());
                }

                // 确保文件名编码正确，支持中文文件名
                String encodedFileName = java.net.URLEncoder.encode(document.getOriginalFileName(), "UTF-8")
                        .replaceAll("\\+", "%20");

                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(document.getMimeType()))
                        .header(HttpHeaders.CONTENT_DISPOSITION,
                               "attachment; filename*=UTF-8''" + encodedFileName)
                        .header("X-Filename", document.getOriginalFileName())
                        .body(resource);
            } else {
                System.out.println("❌ 文件不存在或不可读");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            System.out.println("文档下载异常: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }

    @GetMapping("/search")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Page<Document>>> searchDocuments(
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Long ownerId,
            @RequestParam(required = false) Document.DocumentStatus status,
            @RequestParam(required = false) Document.DocumentClassification classification,
            @RequestParam(required = false) Boolean isCurrentVersion,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : 
                   Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<Document> documents = documentService.searchDocuments(title, description, fileName,
                categoryId, ownerId, status, classification, isCurrentVersion, pageable);
        
        return ResponseEntity.ok(ApiResponse.success("Search completed", documents));
    }

    @GetMapping("/{id}/versions")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getDocumentVersions(@PathVariable Long id) {
        try {
            System.out.println("=== 获取文档版本历史 ===");
            System.out.println("文档ID: " + id);

            List<Document> versions = documentService.getDocumentVersions(id);
            System.out.println("找到版本数量: " + versions.size());

            // 创建简化的版本信息
            List<Map<String, Object>> versionList = versions.stream().map(version -> {
                Map<String, Object> versionInfo = new HashMap<>();
                versionInfo.put("id", version.getId());
                versionInfo.put("versionNumber", version.getVersionNumber());
                versionInfo.put("versionLabel", version.getVersionLabel());
                versionInfo.put("title", version.getTitle());
                versionInfo.put("description", version.getDescription());
                versionInfo.put("fileName", version.getFileName());
                versionInfo.put("originalFileName", version.getOriginalFileName());
                versionInfo.put("fileSize", version.getFileSize());
                versionInfo.put("mimeType", version.getMimeType());
                versionInfo.put("status", version.getStatus().toString());
                versionInfo.put("isCurrentVersion", version.getIsCurrentVersion());
                versionInfo.put("createdAt", version.getCreatedAt().toString());
                versionInfo.put("downloadCount", version.getDownloadCount());
                versionInfo.put("viewCount", version.getViewCount());

                // 安全地添加创建者信息
                if (version.getOwner() != null) {
                    versionInfo.put("createdBy", Map.of(
                        "id", version.getOwner().getId(),
                        "username", version.getOwner().getUsername(),
                        "firstName", version.getOwner().getFirstName(),
                        "lastName", version.getOwner().getLastName()
                    ));
                }

                return versionInfo;
            }).collect(Collectors.toList());

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("versions", versionList);
            responseData.put("totalVersions", versions.size());
            responseData.put("currentVersion", versions.stream()
                    .filter(Document::getIsCurrentVersion)
                    .findFirst()
                    .map(version -> version.getVersionNumber().toString())
                    .orElse("1.0"));

            return ResponseEntity.ok(ApiResponse.success("Document versions retrieved", responseData));
        } catch (RuntimeException e) {
            System.out.println("获取文档版本失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get document versions", e.getMessage()));
        }
    }

    @PostMapping("/{id}/versions/restore/{versionId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> restoreDocumentVersion(
            @PathVariable Long id,
            @PathVariable Long versionId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 恢复文档版本 ===");
            System.out.println("文档ID: " + id + ", 版本ID: " + versionId);

            User user = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            Document restoredVersion = documentService.restoreDocumentVersion(id, versionId, user);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("id", restoredVersion.getId());
            responseData.put("versionNumber", restoredVersion.getVersionNumber());
            responseData.put("title", restoredVersion.getTitle());
            responseData.put("status", restoredVersion.getStatus().toString());

            return ResponseEntity.ok(ApiResponse.success("Document version restored successfully", responseData));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to restore document version", e.getMessage()));
        }
    }

    @PostMapping("/{documentId}/permissions")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Void>> grantPermission(
            @PathVariable Long documentId,
            @RequestParam Long userId,
            @RequestParam DocumentPermission.PermissionType permissionType,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        try {
            User grantedBy = userService.getUserByUsername(userPrincipal.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            documentService.grantPermission(documentId, userId, permissionType, grantedBy);
            return ResponseEntity.ok(ApiResponse.success("Permission granted successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to grant permission", e.getMessage()));
        }
    }

    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN') or hasRole('QA')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getDocumentStats() {
        Map<String, Object> stats = Map.of(
            "totalDocuments", documentService.getTotalDocumentCount(),
            "draftDocuments", documentService.getDocumentCountByStatus(Document.DocumentStatus.DRAFT),
            "publishedDocuments", documentService.getDocumentCountByStatus(Document.DocumentStatus.PUBLISHED),
            "pendingApproval", documentService.getPendingApprovalCount()
        );
        
        return ResponseEntity.ok(ApiResponse.success("Document statistics", stats));
    }

    @GetMapping("/recent")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Document>>> getRecentDocuments(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<Document> recentDocuments = documentService.getRecentDocuments(limit);
        return ResponseEntity.ok(ApiResponse.success("Recent documents retrieved", recentDocuments));
    }

    @GetMapping("/popular")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Document>>> getPopularDocuments(
            @RequestParam(defaultValue = "10") int limit) {

        List<Document> popularDocuments = documentService.getMostDownloadedDocuments(limit);
        return ResponseEntity.ok(ApiResponse.success("Popular documents retrieved", popularDocuments));
    }

    @GetMapping("/{id}/preview")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<Resource> previewDocument(@PathVariable Long id,
                                                   @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            System.out.println("=== 文档预览开始 ===");
            System.out.println("文档ID: " + id);
            System.out.println("用户: " + userPrincipal.getUsername());

            Optional<Document> documentOpt = documentService.getDocumentById(id);
            if (documentOpt.isEmpty()) {
                System.out.println("❌ 文档不存在: " + id);
                return ResponseEntity.notFound().build();
            }

            Document document = documentOpt.get();
            System.out.println("✅ 找到文档: " + document.getTitle());
            System.out.println("文件类型: " + document.getMimeType());
            System.out.println("文件路径: " + document.getFilePath());

            // 简化权限检查，暂时跳过
            System.out.println("跳过权限检查");

            // 检查文件类型是否支持预览
            String mimeType = document.getMimeType();
            if (!isPreviewSupported(mimeType)) {
                System.out.println("❌ 文件类型不支持预览: " + mimeType);
                return ResponseEntity.status(415)
                        .header("X-Error-Message", "File type not supported for preview")
                        .build();
            }

            Path filePath = Paths.get(document.getFilePath());
            System.out.println("完整文件路径: " + filePath.toAbsolutePath());

            Resource resource = new UrlResource(filePath.toUri());
            System.out.println("资源存在: " + resource.exists());
            System.out.println("资源可读: " + resource.isReadable());

            if (resource.exists() && resource.isReadable()) {
                System.out.println("✅ 文件可以预览");

                // Increment view count
                try {
                    documentService.incrementViewCount(id);
                } catch (Exception e) {
                    System.out.println("更新查看计数失败: " + e.getMessage());
                }

                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(mimeType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + document.getOriginalFileName() + "\"")
                        .header("X-Filename", document.getOriginalFileName())
                        .header("X-File-Size", String.valueOf(document.getFileSize()))
                        .body(resource);
            } else {
                System.out.println("❌ 文件不存在或不可读");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            System.out.println("文档预览异常: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }

    private boolean isPreviewSupported(String mimeType) {
        if (mimeType == null) return false;

        // 支持预览的文件类型
        return mimeType.startsWith("text/") ||
               mimeType.equals("application/pdf") ||
               mimeType.startsWith("image/") ||
               mimeType.equals("application/json") ||
               mimeType.equals("application/xml") ||
               mimeType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
               mimeType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
               mimeType.equals("application/vnd.openxmlformats-officedocument.presentationml.presentation");
    }

    @GetMapping("/{id}/preview-info")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getPreviewInfo(@PathVariable Long id) {
        try {
            Optional<Document> documentOpt = documentService.getDocumentById(id);
            if (documentOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            Document document = documentOpt.get();
            Map<String, Object> previewInfo = new HashMap<>();
            previewInfo.put("id", document.getId());
            previewInfo.put("title", document.getTitle());
            previewInfo.put("originalFileName", document.getOriginalFileName());
            previewInfo.put("mimeType", document.getMimeType());
            previewInfo.put("fileSize", document.getFileSize());
            previewInfo.put("previewSupported", isPreviewSupported(document.getMimeType()));
            previewInfo.put("previewUrl", "/dms/api/documents/" + id + "/preview");

            return ResponseEntity.ok(ApiResponse.success("Preview info retrieved", previewInfo));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("Failed to get preview info", e.getMessage()));
        }
    }
}
