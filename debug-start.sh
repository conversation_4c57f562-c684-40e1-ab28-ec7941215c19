#!/bin/bash

echo "🔧 制药DMS调试启动脚本"
echo "=========================="

# 切换到应用目录
cd /d/learn/java/dms

echo "📍 当前目录: $(pwd)"
echo "☕ Java版本:"
java -version

echo ""
echo "📦 检查JAR文件:"
if [ -f "target/dms-1.0.0.jar" ]; then
    echo "✅ JAR文件存在: $(ls -lh target/dms-1.0.0.jar)"
else
    echo "❌ JAR文件不存在"
    exit 1
fi

echo ""
echo "🗄️ 启动PostgreSQL数据库:"
/d/sql/pgsql/bin/pg_ctl -D /d/sql/pgsql/data start

echo ""
echo "⏳ 等待数据库启动..."
sleep 5

echo ""
echo "🔗 测试数据库连接:"
/d/sql/pgsql/bin/psql -U postgres -c "SELECT version();" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ 数据库连接成功"
else
    echo "⚠️ 数据库连接失败，使用H2数据库"
    PROFILE="h2"
fi

echo ""
echo "🚀 启动应用程序 (Profile: ${PROFILE:-postgresql}):"
echo "命令: java -jar target/dms-1.0.0.jar --spring.profiles.active=${PROFILE:-postgresql} --server.address=0.0.0.0"

# 启动应用程序并显示输出
java -jar target/dms-1.0.0.jar \
    --spring.profiles.active=${PROFILE:-postgresql} \
    --server.address=0.0.0.0 \
    --logging.level.com.pharma.dms=INFO \
    --logging.level.org.springframework.boot=INFO
