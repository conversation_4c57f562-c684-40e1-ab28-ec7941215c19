<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统概览 - 制药文档管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" style="min-height: 100vh; background-color: #f8f9fa; border-right: 1px solid #dee2e6;">
                <div class="position-sticky pt-3">
                    <div class="navbar-brand mb-4" style="font-weight: bold; color: #0d6efd !important;">
                        <i class="fas fa-pills me-2"></i>
                        制药文档管理系统
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/users" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/documents" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-file-alt me-2"></i>
                                文档管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/system-overview" style="background-color: #0d6efd; color: white !important; border-radius: 0.375rem;">
                                <i class="fas fa-chart-line me-2"></i>
                                系统概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/my-training" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user-graduate me-2"></i>
                                我的培训
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/training-courses" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-graduation-cap me-2"></i>
                                培训课程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-bar me-2"></i>
                                报表管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-cog me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/profile" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user me-2"></i>
                                个人资料
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">系统概览</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button class="btn btn-primary" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-2"></i>刷新数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Page content -->
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <h1 class="h3 mb-0 text-gray-800">系统概览</h1>
            <button class="btn btn-primary" onclick="refreshData()">
                <i class="fas fa-sync-alt me-2"></i>刷新数据
            </button>
        </div>

        <!-- Statistics Cards Row -->
        <div class="row">
            <!-- Total Documents Card -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    文档总数</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalDocuments">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Users Card -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    活跃用户</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeUsers">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Reviews Card -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Pending Reviews</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="pendingReviews">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Alerts Card -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    系统警报</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="systemAlerts">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Row -->
        <div class="row">
            <!-- Document Categories Chart -->
            <div class="col-xl-6 col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">文档分类统计</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-pie pt-4 pb-2">
                            <canvas id="categoryChart"></canvas>
                        </div>
                        <div class="mt-4 text-center small" id="categoryLegend">
                            <!-- Legend will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Document Status Chart -->
            <div class="col-xl-6 col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">文档状态统计</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-pie pt-4 pb-2">
                            <canvas id="statusChart"></canvas>
                        </div>
                        <div class="mt-4 text-center small" id="statusLegend">
                            <!-- Legend will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">最近文档</h6>
                    </div>
                    <div class="card-body">
                        <div id="recentDocuments">
                            <!-- Recent documents will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">系统活动</h6>
                    </div>
                    <div class="card-body">
                        <div id="systemActivity">
                            <!-- System activity will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Storage Usage -->
        <div class="row">
            <div class="col-lg-12 mb-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Storage Usage</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-0 font-weight-bold text-primary" id="totalStorage">0 GB</div>
                                    <div class="text-xs text-gray-600">Total Storage</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-0 font-weight-bold text-success" id="usedStorage">0 GB</div>
                                    <div class="text-xs text-gray-600">Used Storage</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-0 font-weight-bold text-info" id="freeStorage">0 GB</div>
                                    <div class="text-xs text-gray-600">Free Storage</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h4 mb-0 font-weight-bold text-warning" id="storageUsage">0%</div>
                                    <div class="text-xs text-gray-600">Usage Percentage</div>
                                </div>
                            </div>
                        </div>
                        <div class="progress mt-3">
                            <div class="progress-bar" role="progressbar" id="storageProgressBar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let categoryChart, statusChart;

        document.addEventListener('DOMContentLoaded', function() {
            loadSystemOverview();
            initializeCharts();
        });

        async function loadSystemOverview() {
            try {
                // Load dashboard statistics
                const response = await authUtils.secureApiCall('/dms/api/dashboard/stats');

                if (response.ok) {
                    const result = await response.json();
                    const stats = result.data;
                    
                    document.getElementById('totalDocuments').textContent = stats.totalDocuments || '0';
                    document.getElementById('activeUsers').textContent = stats.activeUsers || '0';
                    document.getElementById('pendingReviews').textContent = stats.pendingReviews || '0';
                    document.getElementById('systemAlerts').textContent = stats.systemAlerts || '0';
                } else {
                    // 显示零值而不是演示数据
                    console.warn('无法加载系统统计数据，显示默认值');
                    document.getElementById('totalDocuments').textContent = '0';
                    document.getElementById('activeUsers').textContent = '0';
                    document.getElementById('pendingReviews').textContent = '0';
                    document.getElementById('systemAlerts').textContent = '0';
                }

                // Load other data
                loadRecentDocuments();
                loadSystemActivity();
                loadStorageInfo();
                updateCharts();
            } catch (error) {
                console.error('Error loading system overview:', error);
                // 显示零值而不是演示数据
                document.getElementById('totalDocuments').textContent = '0';
                document.getElementById('activeUsers').textContent = '0';
                document.getElementById('pendingReviews').textContent = '0';
                document.getElementById('systemAlerts').textContent = '0';

                // 显示错误提示
                showErrorMessage('系统统计数据加载失败，请刷新页面重试');
            }
        }

        function initializeCharts() {
            // Category Chart
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            categoryChart = new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['标准操作程序', '质量手册', '验证文档', '培训材料', '其他'],
                    datasets: [{
                        data: [45, 25, 15, 10, 5],
                        backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
                        hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf', '#f4b619', '#e02d1b'],
                        hoverBorderColor: "rgba(234, 236, 244, 1)",
                    }],
                },
                options: {
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    cutout: '80%',
                },
            });

            // Status Chart
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            statusChart = new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Published', 'Draft', 'Under Review', 'Approved', 'Archived'],
                    datasets: [{
                        data: [60, 20, 10, 8, 2],
                        backgroundColor: ['#1cc88a', '#6c757d', '#f6c23e', '#4e73df', '#e74a3b'],
                        hoverBackgroundColor: ['#17a673', '#5a6268', '#f4b619', '#2e59d9', '#e02d1b'],
                        hoverBorderColor: "rgba(234, 236, 244, 1)",
                    }],
                },
                options: {
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    cutout: '80%',
                },
            });
        }

        function loadRecentDocuments() {
            const recentDocs = [
                { title: 'SOP-001 Manufacturing Process', date: '2024-12-04', author: 'John Doe' },
                { title: 'Quality Manual v2.1', date: '2024-12-03', author: 'Jane Smith' },
                { title: 'Validation Protocol VP-001', date: '2024-12-02', author: 'Mike Johnson' },
                { title: 'Training Material - GMP Basics', date: '2024-12-01', author: 'Sarah Wilson' },
                { title: 'Regulatory Submission FDA-001', date: '2024-11-30', author: 'David Brown' }
            ];

            const container = document.getElementById('recentDocuments');
            container.innerHTML = '';

            recentDocs.forEach(doc => {
                const item = document.createElement('div');
                item.className = 'mb-3 p-2 border-left border-primary';
                item.innerHTML = `
                    <div class="font-weight-bold">${doc.title}</div>
                    <div class="text-muted small">by ${doc.author} on ${doc.date}</div>
                `;
                container.appendChild(item);
            });
        }

        async function loadSystemActivity() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/audit/recent?limit=5');

                if (response.ok) {
                    const result = await response.json();
                    const activities = result.data || [];

                    const container = document.getElementById('systemActivity');
                    container.innerHTML = '';

                    if (activities.length === 0) {
                        container.innerHTML = '<div class="text-center text-muted p-3">暂无系统活动记录</div>';
                        return;
                    }

                    activities.forEach(activity => {
                        const item = document.createElement('div');
                        item.className = 'mb-3 p-2 border-left border-info';
                        item.innerHTML = `
                            <div class="font-weight-bold">${activity.action || '系统操作'}</div>
                            <div class="text-muted small">by ${activity.username || '系统'} - ${formatTimeAgo(activity.timestamp)}</div>
                        `;
                        container.appendChild(item);
                    });
                } else {
                    throw new Error('Failed to load system activity');
                }
            } catch (error) {
                console.error('Error loading system activity:', error);
                const container = document.getElementById('systemActivity');
                container.innerHTML = '<div class="text-center text-muted p-3">系统活动加载失败</div>';
            }
        }

        function loadStorageInfo() {
            // Demo storage data
            const totalGB = 100;
            const usedGB = 35;
            const freeGB = totalGB - usedGB;
            const usagePercent = Math.round((usedGB / totalGB) * 100);

            document.getElementById('totalStorage').textContent = totalGB + ' GB';
            document.getElementById('usedStorage').textContent = usedGB + ' GB';
            document.getElementById('freeStorage').textContent = freeGB + ' GB';
            document.getElementById('storageUsage').textContent = usagePercent + '%';

            const progressBar = document.getElementById('storageProgressBar');
            progressBar.style.width = usagePercent + '%';
            progressBar.setAttribute('aria-valuenow', usagePercent);

            // Change color based on usage
            if (usagePercent > 80) {
                progressBar.className = 'progress-bar bg-danger';
            } else if (usagePercent > 60) {
                progressBar.className = 'progress-bar bg-warning';
            } else {
                progressBar.className = 'progress-bar bg-success';
            }
        }

        function updateCharts() {
            // Update chart data if needed
            if (categoryChart) {
                categoryChart.update();
            }
            if (statusChart) {
                statusChart.update();
            }
        }

        function refreshData() {
            loadSystemOverview();
        }

        // 格式化时间显示
        function formatTimeAgo(timestamp) {
            if (!timestamp) return '未知时间';

            const now = new Date();
            const time = new Date(timestamp);
            const diffMs = now - time;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            if (diffMins < 1) return '刚刚';
            if (diffMins < 60) return `${diffMins}分钟前`;
            if (diffHours < 24) return `${diffHours}小时前`;
            if (diffDays < 7) return `${diffDays}天前`;

            return time.toLocaleDateString('zh-CN');
        }

        // 显示错误消息
        function showErrorMessage(message) {
            console.error(message);
            // 简单的提示，可以后续改为更好的UI组件
            if (typeof toastr !== 'undefined') {
                toastr.error(message);
            }
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 认证工具 -->
    <script src="/dms/js/auth.js"></script>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
