<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的培训 - 制药文档管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" style="min-height: 100vh; background-color: #f8f9fa; border-right: 1px solid #dee2e6;">
                <div class="position-sticky pt-3">
                    <div class="navbar-brand mb-4" style="font-weight: bold; color: #0d6efd !important;">
                        <i class="fas fa-pills me-2"></i>
                        制药文档管理系统
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/users" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/documents" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-file-alt me-2"></i>
                                文档管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/system-overview" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-line me-2"></i>
                                系统概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/my-training" style="background-color: #0d6efd; color: white !important; border-radius: 0.375rem;">
                                <i class="fas fa-user-graduate me-2"></i>
                                我的培训
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/training-courses" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-graduation-cap me-2"></i>
                                培训课程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-bar me-2"></i>
                                报表管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-cog me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/profile" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user me-2"></i>
                                个人资料
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">我的培训</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button class="btn btn-primary" onclick="browseAllCourses()">
                                <i class="fas fa-search me-2"></i>浏览所有课程
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Page content -->
        <!-- Page Header -->
        <div class="d-sm-flex align-items-center justify-content-between mb-4">
            <h1 class="h3 mb-0 text-gray-800">我的培训</h1>
            <div>
                <button class="btn btn-primary" onclick="browseAllCourses()">
                    <i class="fas fa-search me-2"></i>浏览所有课程
                </button>
            </div>
        </div>

        <!-- Training Progress Overview -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    分配课程</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="assignedCourses">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-tasks fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    已完成</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="completedCourses">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    进行中</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="inProgressCourses">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    已逾期</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="overdueCourses">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Training Tabs -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <ul class="nav nav-tabs card-header-tabs" id="trainingTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="assigned-tab" data-bs-toggle="tab" data-bs-target="#assigned"
                                type="button" role="tab" aria-controls="assigned" aria-selected="true">
                            <i class="fas fa-tasks me-2"></i>分配课程
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" 
                                type="button" role="tab" aria-controls="completed" aria-selected="false">
                            <i class="fas fa-check-circle me-2"></i>已完成课程
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="certificates-tab" data-bs-toggle="tab" data-bs-target="#certificates" 
                                type="button" role="tab" aria-controls="certificates" aria-selected="false">
                            <i class="fas fa-certificate me-2"></i>培训证书
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" 
                                type="button" role="tab" aria-controls="history" aria-selected="false">
                            <i class="fas fa-history me-2"></i>培训历史
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="trainingTabsContent">
                    <!-- Assigned Courses Tab -->
                    <div class="tab-pane fade show active" id="assigned" role="tabpanel" aria-labelledby="assigned-tab">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>课程</th>
                                        <th>类型</th>
                                        <th>优先级</th>
                                        <th>截止日期</th>
                                        <th>进度</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="assignedCoursesTable">
                                    <!-- Assigned courses will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Completed Courses Tab -->
                    <div class="tab-pane fade" id="completed" role="tabpanel" aria-labelledby="completed-tab">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>课程</th>
                                        <th>完成日期</th>
                                        <th>分数</th>
                                        <th>证书</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="completedCoursesTable">
                                    <!-- Completed courses will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Certificates Tab -->
                    <div class="tab-pane fade" id="certificates" role="tabpanel" aria-labelledby="certificates-tab">
                        <div class="row" id="certificatesGrid">
                            <!-- Certificates will be loaded here -->
                        </div>
                    </div>

                    <!-- Training History Tab -->
                    <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>课程</th>
                                        <th>开始日期</th>
                                        <th>完成日期</th>
                                        <th>尝试次数</th>
                                        <th>最终分数</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody id="trainingHistoryTable">
                                    <!-- Training history will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Electronic Signature Modal -->
        <div class="modal fade" id="signatureModal" tabindex="-1" aria-labelledby="signatureModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="signatureModalLabel">Electronic Signature Required</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="signatureForm">
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>GMP合规要求：</strong> 需要您的电子签名来证明完成此培训。
                            </div>
                            <input type="hidden" id="signatureRecordId" name="recordId">
                            <div class="mb-3">
                                <label for="electronicSignature" class="form-label">电子签名 *</label>
                                <input type="text" class="form-control" id="electronicSignature" name="signature"
                                       placeholder="请输入您的全名作为电子签名" required>
                                <div class="form-text">通过输入您的姓名，您确认已完成此培训。</div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="signatureConfirm" required>
                                    <label class="form-check-label" for="signatureConfirm">
                                        我确认已完成此培训并理解培训内容。
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-signature me-2"></i>Sign & Certify
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadMyTrainingData();
            
            // Load data when tabs are switched
            document.querySelectorAll('#trainingTabs button').forEach(tab => {
                tab.addEventListener('shown.bs.tab', function(e) {
                    const target = e.target.getAttribute('data-bs-target');
                    switch(target) {
                        case '#assigned':
                            loadAssignedCourses();
                            break;
                        case '#completed':
                            loadCompletedCourses();
                            break;
                        case '#certificates':
                            loadCertificates();
                            break;
                        case '#history':
                            loadTrainingHistory();
                            break;
                    }
                });
            });
        });

        async function loadMyTrainingData() {
            await loadTrainingStats();
            await loadAssignedCourses();
        }

        async function loadTrainingStats() {
            try {
                console.log('加载个人培训统计数据');
                const response = await authUtils.secureApiCall('/dms/api/training/my-stats');

                if (response.ok) {
                    const result = await response.json();
                    const stats = result.data;

                    console.log('个人培训统计数据:', stats);

                    // 显示真实统计数据
                    document.getElementById('assignedCourses').textContent = stats.assignedCourses || '0';
                    document.getElementById('completedCourses').textContent = stats.completedCourses || '0';
                    document.getElementById('inProgressCourses').textContent = stats.inProgressCourses || '0';
                    document.getElementById('overdueCourses').textContent = stats.overdueCourses || '0';

                    console.log('✅ 个人培训统计数据更新完成');
                } else {
                    console.warn('获取个人培训统计失败，使用默认值');
                    // 使用默认值而不是硬编码
                    document.getElementById('assignedCourses').textContent = '0';
                    document.getElementById('completedCourses').textContent = '0';
                    document.getElementById('inProgressCourses').textContent = '0';
                    document.getElementById('overdueCourses').textContent = '0';
                }
            } catch (error) {
                console.error('Error loading training stats:', error);
                // 使用默认值而不是硬编码
                document.getElementById('assignedCourses').textContent = '0';
                document.getElementById('completedCourses').textContent = '0';
                document.getElementById('inProgressCourses').textContent = '0';
                document.getElementById('overdueCourses').textContent = '0';
            }
        }

        async function loadAssignedCourses() {
            try {
                console.log('加载分配的培训课程');
                const response = await authUtils.secureApiCall('/dms/api/training/my-assignments');

                if (response.ok) {
                    const result = await response.json();
                    const assignedCourses = result.data;

                    console.log('分配的培训课程:', assignedCourses);
                    displayAssignedCourses(assignedCourses);
                    console.log('✅ 分配的培训课程加载完成');
                } else {
                    console.warn('获取分配的培训课程失败，显示空列表');
                    displayAssignedCourses([]);
                }
            } catch (error) {
                console.error('Error loading assigned courses:', error);
                displayAssignedCourses([]);
            }
        }

        function displayAssignedCourses(courses) {
            const tbody = document.getElementById('assignedCoursesTable');
            tbody.innerHTML = '';

            if (!courses || courses.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-muted text-center">暂无分配的培训课程</td></tr>';
                return;
            }

            courses.forEach(assignment => {
                const row = document.createElement('tr');
                const isOverdue = new Date(assignment.dueDate) < new Date();
                
                row.innerHTML = `
                    <td>
                        <div class="fw-bold">${assignment.course.title}</div>
                        <span class="badge ${getCourseTypeBadgeClass(assignment.course.courseType)}">${assignment.course.courseType}</span>
                    </td>
                    <td>
                        <span class="badge ${getCourseTypeBadgeClass(assignment.course.courseType)}">${assignment.course.courseType}</span>
                    </td>
                    <td>
                        <span class="badge ${getPriorityBadgeClass(assignment.priority)}">${assignment.priority}</span>
                    </td>
                    <td>
                        <span class="${isOverdue ? 'text-danger fw-bold' : ''}">${formatDate(assignment.dueDate)}</span>
                        ${isOverdue ? '<br><small class="text-danger">OVERDUE</small>' : ''}
                    </td>
                    <td>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar ${getProgressBarClass(assignment.progress)}" 
                                 role="progressbar" style="width: ${assignment.progress}%" 
                                 aria-valuenow="${assignment.progress}" aria-valuemin="0" aria-valuemax="100">
                                ${assignment.progress}%
                            </div>
                        </div>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="startTraining(${assignment.id})" 
                                ${assignment.progress > 0 ? 'title="Continue Training"' : 'title="Start Training"'}>
                            <i class="fas fa-play"></i> ${assignment.progress > 0 ? '继续' : '开始'}
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="viewCourseDetails(${assignment.course.id})" title="View Details">
                            <i class="fas fa-info-circle"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        async function loadCompletedCourses() {
            try {
                console.log('加载已完成的培训课程');
                const response = await authUtils.secureApiCall('/dms/api/training/my-completed');

                if (response.ok) {
                    const result = await response.json();
                    const completedCourses = result.data;

                    console.log('已完成的培训课程:', completedCourses);
                    displayCompletedCourses(completedCourses);
                    console.log('✅ 已完成的培训课程加载完成');
                } else {
                    console.warn('获取已完成的培训课程失败，显示空列表');
                    displayCompletedCourses([]);
                }
            } catch (error) {
                console.error('Error loading completed courses:', error);
                displayCompletedCourses([]);
            }
        }

        function displayCompletedCourses(courses) {
            const tbody = document.getElementById('completedCoursesTable');
            tbody.innerHTML = '';

            if (!courses || courses.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-muted text-center">暂无已完成的培训课程</td></tr>';
                return;
            }

            courses.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="fw-bold">${record.course.title}</div>
                        <span class="badge ${getCourseTypeBadgeClass(record.course.courseType)}">${record.course.courseType}</span>
                    </td>
                    <td>${formatDate(record.completionDate)}</td>
                    <td>
                        <span class="badge ${getScoreBadgeClass(record.finalScore)}">${record.finalScore}%</span>
                    </td>
                    <td>
                        ${record.certificateNumber ? 
                          `<small class="text-muted">${record.certificateNumber}</small>` : 
                          '<span class="text-muted">无证书</span>'}
                    </td>
                    <td>
                        <span class="badge ${getStatusBadgeClass(record.status)}">${record.status}</span>
                    </td>
                    <td>
                        ${record.certificateNumber ? 
                          `<button class="btn btn-sm btn-success me-1" onclick="downloadCertificate('${record.certificateNumber}')" title="下载证书">
                               <i class="fas fa-download"></i>
                           </button>` : ''}
                        <button class="btn btn-sm btn-outline-info" onclick="viewTrainingRecord(${record.id})" title="查看记录">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Electronic signature form submission
        document.getElementById('signatureForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const recordId = formData.get('recordId');
            const signature = formData.get('signature');

            try {
                const response = await fetch(`/dms/api/training/records/${recordId}/sign`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + localStorage.getItem('token')
                    },
                    body: JSON.stringify({ signature: signature })
                });

                const result = await response.json();

                if (response.ok) {
                    alert('电子签名添加成功！培训已获得认证。');
                    bootstrap.Modal.getInstance(document.getElementById('signatureModal')).hide();
                    this.reset();
                    loadMyTrainingData();
                } else {
                    alert('Error adding signature: ' + result.message);
                }
            } catch (error) {
                alert('Error adding signature: Network error');
                console.error('Error:', error);
            }
        });

        // Utility functions
        function getCourseTypeBadgeClass(type) {
            switch (type) {
                case 'GMP': return 'bg-danger';
                case 'SOP': return 'bg-primary';
                case 'SAFETY': return 'bg-warning';
                case 'QUALITY': return 'bg-success';
                case 'REGULATORY': return 'bg-info';
                case 'TECHNICAL': return 'bg-dark';
                case 'COMPLIANCE': return 'bg-purple';
                default: return 'bg-secondary';
            }
        }

        function getPriorityBadgeClass(priority) {
            switch (priority) {
                case 'URGENT': return 'bg-danger';
                case 'HIGH': return 'bg-warning';
                case 'NORMAL': return 'bg-info';
                case 'LOW': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        function getProgressBarClass(progress) {
            if (progress >= 80) return 'bg-success';
            if (progress >= 50) return 'bg-warning';
            return 'bg-danger';
        }

        function getScoreBadgeClass(score) {
            if (score >= 90) return 'bg-success';
            if (score >= 80) return 'bg-warning';
            return 'bg-danger';
        }

        function getStatusBadgeClass(status) {
            switch (status) {
                case 'CERTIFIED': return 'bg-success';
                case 'COMPLETED': return 'bg-primary';
                case 'IN_PROGRESS': return 'bg-warning';
                case 'FAILED': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        function browseAllCourses() {
            window.location.href = '/dms/training-courses';
        }

        function startTraining(assignmentId) {
            window.location.href = `/dms/training/assignment/${assignmentId}/start`;
        }

        function viewCourseDetails(courseId) {
            alert('Course details functionality will be implemented');
        }

        async function downloadCertificate(certificateNumber) {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    alert('请先登录');
                    return;
                }

                console.log('=== 证书下载开始 ===');
                console.log('证书编号:', certificateNumber);

                const response = await fetch(`/dms/api/test/certificate-download/${certificateNumber}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });

                console.log('证书下载响应状态:', response.status);

                if (response.ok) {
                    // 获取文件名
                    const contentDisposition = response.headers.get('Content-Disposition');
                    let filename = `certificate-${certificateNumber}.pdf`;
                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                        if (filenameMatch) {
                            filename = filenameMatch[1];
                        }
                    }

                    // 创建下载链接
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    console.log('证书下载成功:', filename);
                } else {
                    const errorText = await response.text();
                    console.error('证书下载失败:', errorText);
                    alert('证书下载失败: ' + response.status);
                }
            } catch (error) {
                console.error('证书下载异常:', error);
                alert('证书下载失败: 网络错误');
            }
        }

        function viewTrainingRecord(recordId) {
            alert('Training record details functionality will be implemented');
        }

        function showSignatureModal(recordId) {
            document.getElementById('signatureRecordId').value = recordId;
            new bootstrap.Modal(document.getElementById('signatureModal')).show();
        }

        // Placeholder functions for other tabs
        function loadCertificates() {
            const grid = document.getElementById('certificatesGrid');
            grid.innerHTML = '<div class="col-12"><p class="text-muted">证书将在此处显示</p></div>';
        }

        function loadTrainingHistory() {
            const tbody = document.getElementById('trainingHistoryTable');
            tbody.innerHTML = '<tr><td colspan="6" class="text-muted text-center">培训历史将在此处显示</td></tr>';
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 认证工具 -->
    <script src="/dms/js/auth.js"></script>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
