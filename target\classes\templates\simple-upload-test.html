<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化上传测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">📤 简化上传测试</h2>
            
            <!-- 认证信息 -->
            <div class="card mb-3">
                <div class="card-body">
                    <h6>认证状态</h6>
                    <div id="auth-info" class="text-muted">检查中...</div>
                </div>
            </div>

            <!-- 上传表单 -->
            <div class="card mb-3">
                <div class="card-body">
                    <h6>文件上传</h6>
                    <form id="simpleUploadForm">
                        <div class="mb-3">
                            <label for="file" class="form-label">选择文件</label>
                            <input type="file" class="form-control" id="file" name="file" required>
                        </div>
                        <div class="mb-3">
                            <label for="title" class="form-label">标题</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">描述</label>
                            <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">上传</button>
                        <button type="button" class="btn btn-secondary" onclick="clearLog()">清除日志</button>
                    </form>
                </div>
            </div>

            <!-- 日志区域 -->
            <div class="card">
                <div class="card-body">
                    <h6>调试日志</h6>
                    <div id="log" class="log-area">等待操作...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let logContent = '';

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContent += `[${timestamp}] ${message}\n`;
            document.getElementById('log').textContent = logContent;
            document.getElementById('log').scrollTop = document.getElementById('log').scrollHeight;
            console.log(message);
        }

        function clearLog() {
            logContent = '';
            document.getElementById('log').textContent = '日志已清除...';
        }

        // 检查认证状态
        function checkAuth() {
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            
            if (!token) {
                document.getElementById('auth-info').innerHTML = '<span class="text-danger">❌ 未找到Token</span>';
                log('ERROR: 未找到认证Token');
                return null;
            }

            if (!user) {
                document.getElementById('auth-info').innerHTML = '<span class="text-danger">❌ 未找到用户信息</span>';
                log('ERROR: 未找到用户信息');
                return null;
            }

            try {
                const userObj = JSON.parse(user);
                document.getElementById('auth-info').innerHTML = `<span class="text-success">✅ 用户: ${userObj.username}</span>`;
                log(`SUCCESS: 认证用户 ${userObj.username}`);
                return token;
            } catch (e) {
                document.getElementById('auth-info').innerHTML = '<span class="text-danger">❌ 用户信息解析失败</span>';
                log('ERROR: 用户信息解析失败 - ' + e.message);
                return null;
            }
        }

        // 页面加载时检查认证
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            
            // 设置表单提交处理
            document.getElementById('simpleUploadForm').addEventListener('submit', handleUpload);
        });

        async function handleUpload(e) {
            e.preventDefault();
            
            log('=== 开始上传流程 ===');
            
            // 检查认证
            const token = checkAuth();
            if (!token) {
                log('ERROR: 认证失败，停止上传');
                return;
            }

            // 获取表单数据
            const form = e.target;
            const formData = new FormData(form);
            
            const file = formData.get('file');
            const title = formData.get('title');
            const description = formData.get('description');

            // 验证数据
            if (!file || file.size === 0) {
                log('ERROR: 未选择文件');
                return;
            }

            if (!title || title.trim() === '') {
                log('ERROR: 标题不能为空');
                return;
            }

            log(`INFO: 文件名 = ${file.name}`);
            log(`INFO: 文件大小 = ${file.size} bytes`);
            log(`INFO: 文件类型 = ${file.type}`);
            log(`INFO: 标题 = ${title}`);
            log(`INFO: 描述 = ${description || '(空)'}`);

            try {
                log('INFO: 准备发送请求...');
                
                const response = await fetch('/dms/api/documents/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + token
                        // 不设置Content-Type，让浏览器自动设置
                    },
                    body: formData
                });

                log(`INFO: 响应状态 = ${response.status} ${response.statusText}`);

                if (response.ok) {
                    const result = await response.json();
                    log('SUCCESS: 上传成功!');
                    log(`SUCCESS: 响应数据 = ${JSON.stringify(result, null, 2)}`);
                    alert('上传成功！');
                    form.reset();
                } else {
                    const errorText = await response.text();
                    log(`ERROR: 上传失败 - ${errorText}`);
                    alert('上传失败: ' + errorText);
                }
            } catch (error) {
                log(`ERROR: 网络异常 - ${error.message}`);
                log(`ERROR: 错误堆栈 - ${error.stack}`);
                alert('上传失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
