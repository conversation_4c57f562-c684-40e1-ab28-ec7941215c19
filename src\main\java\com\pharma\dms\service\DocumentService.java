package com.pharma.dms.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.pharma.dms.entity.AuditLog;
import com.pharma.dms.entity.Document;
import com.pharma.dms.entity.DocumentCategory;
import com.pharma.dms.entity.DocumentPermission;
import com.pharma.dms.entity.User;
import com.pharma.dms.repository.DocumentCategoryRepository;
import com.pharma.dms.repository.DocumentPermissionRepository;
import com.pharma.dms.repository.DocumentRepository;
import com.pharma.dms.repository.DocumentTagRepository;
import com.pharma.dms.repository.UserRepository;

@Service
@Transactional
public class DocumentService {

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private DocumentCategoryRepository categoryRepository;

    @Autowired
    private DocumentTagRepository tagRepository;

    @Autowired
    private DocumentPermissionRepository permissionRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AuditService auditService;

    @Value("${app.file.upload-dir:uploads}")
    private String uploadDir;

    @Value("${app.file.max-size:50MB}")
    private String maxFileSize;

    // Document CRUD operations
    public List<Document> getAllDocuments() {
        return documentRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Page<Document> getAllDocuments(Pageable pageable) {
        try {
            System.out.println("=== DocumentService.getAllDocuments ===");
            System.out.println("Pageable: " + pageable);

            // 使用简单的findAll方法，避免复杂的JOIN查询
            Page<Document> documents = documentRepository.findAll(pageable);
            System.out.println("查询到文档数量: " + documents.getTotalElements());

            // 在事务内初始化懒加载属性，避免LazyInitializationException
            documents.getContent().forEach(doc -> {
                try {
                    // 初始化owner
                    if (doc.getOwner() != null) {
                        String username = doc.getOwner().getUsername();
                        System.out.println("初始化文档所有者: " + username);
                    }

                    // 初始化category
                    if (doc.getCategory() != null) {
                        String categoryName = doc.getCategory().getName();
                        System.out.println("初始化文档分类: " + categoryName);
                    }

                    // 初始化tags
                    if (doc.getTags() != null) {
                        int tagCount = doc.getTags().size();
                        System.out.println("初始化文档标签数量: " + tagCount);
                    }
                } catch (Exception e) {
                    System.err.println("初始化文档关联对象失败: " + e.getMessage());
                    // 不抛出异常，继续处理其他文档
                }
            });

            return documents;
        } catch (Exception e) {
            System.err.println("获取文档列表失败: " + e.getMessage());
            e.printStackTrace();
            // 返回空页面而不是抛出异常
            return Page.empty(pageable);
        }
    }

    public Page<Document> getDocumentsByStatus(Document.DocumentStatus status, Pageable pageable) {
        return documentRepository.findByStatus(status, pageable);
    }

    public Document updateDocument(Document document) {
        return documentRepository.save(document);
    }

    public Optional<Document> getDocumentById(Long id) {
        return documentRepository.findById(id);
    }

    public List<Document> getDocumentsByOwner(User owner) {
        return documentRepository.findByOwner(owner);
    }

    public List<Document> getDocumentsByCategory(DocumentCategory category) {
        return documentRepository.findByCategory(category);
    }

    public List<Document> getCurrentVersionDocuments() {
        return documentRepository.findCurrentVersionDocuments();
    }

    // File upload and management
    public Document uploadDocument(MultipartFile file, String title, String description,
                                 Long categoryId, User owner) throws IOException {
        System.out.println("=== 文档上传开始 ===");
        System.out.println("文件名: " + file.getOriginalFilename());
        System.out.println("文件大小: " + file.getSize());
        System.out.println("上传目录: " + uploadDir);

        // Validate file
        if (file.isEmpty()) {
            System.out.println("错误: 文件为空");
            throw new RuntimeException("File is empty");
        }

        // Create upload directory if it doesn't exist
        Path uploadPath = Paths.get(uploadDir);
        System.out.println("上传路径: " + uploadPath.toAbsolutePath());

        try {
            if (!Files.exists(uploadPath)) {
                System.out.println("创建上传目录...");
                Files.createDirectories(uploadPath);
                System.out.println("上传目录创建成功");
            } else {
                System.out.println("上传目录已存在");
            }
        } catch (IOException e) {
            System.out.println("创建上传目录失败: " + e.getMessage());
            throw new RuntimeException("Failed to create upload directory", e);
        }

        // Generate unique filename
        String originalFileName = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFileName);
        String uniqueFileName = UUID.randomUUID().toString() + "." + fileExtension;
        Path filePath = uploadPath.resolve(uniqueFileName);

        System.out.println("原始文件名: " + originalFileName);
        System.out.println("文件扩展名: " + fileExtension);
        System.out.println("唯一文件名: " + uniqueFileName);
        System.out.println("完整文件路径: " + filePath.toAbsolutePath());

        String checksum;
        try {
            // Copy file to upload directory
            System.out.println("开始复制文件...");
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            System.out.println("文件复制成功");

            // Calculate file checksum
            System.out.println("计算文件校验和...");
            checksum = calculateChecksum(file.getBytes());
            System.out.println("校验和计算完成: " + checksum.substring(0, 16) + "...");
        } catch (IOException e) {
            System.out.println("文件操作失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to save file", e);
        }

        // Create document entity
        System.out.println("创建文档实体...");
        Document document = new Document();
        document.setTitle(title);
        document.setDescription(description);
        document.setFileName(uniqueFileName);
        document.setOriginalFileName(originalFileName);
        document.setFilePath(filePath.toString());
        document.setFileSize(file.getSize());
        document.setMimeType(file.getContentType());
        document.setFileExtension(fileExtension);
        document.setChecksum(checksum);
        document.setOwner(owner);

        // Set category if provided
        if (categoryId != null) {
            DocumentCategory category = categoryRepository.findById(categoryId)
                    .orElseThrow(() -> new RuntimeException("Category not found"));
            document.setCategory(category);
        }

        Document savedDocument = documentRepository.save(document);

        // Log audit event
        auditService.logSystemEvent("DOCUMENT_UPLOADED", 
                "Document uploaded: " + originalFileName + " by " + owner.getUsername(), 
                AuditLog.Severity.INFO);

        return savedDocument;
    }

    public Document createNewVersion(Long parentDocumentId, MultipartFile file, 
                                   String versionLabel, User owner) throws IOException {
        Document parentDocument = documentRepository.findById(parentDocumentId)
                .orElseThrow(() -> new RuntimeException("Parent document not found"));

        // Mark current version as not current
        parentDocument.setIsCurrentVersion(false);
        documentRepository.save(parentDocument);

        // Create new version
        Document newVersion = uploadDocument(file, parentDocument.getTitle(), 
                                           parentDocument.getDescription(), 
                                           parentDocument.getCategory() != null ? parentDocument.getCategory().getId() : null, 
                                           owner);

        // Set version information
        newVersion.setParentDocument(parentDocument);
        newVersion.setVersionNumber(getNextVersionNumber(parentDocument));
        newVersion.setVersionLabel(versionLabel);
        newVersion.setIsCurrentVersion(true);

        Document savedVersion = documentRepository.save(newVersion);

        // Log audit event
        auditService.logSystemEvent("DOCUMENT_VERSION_CREATED", 
                "New version created for document: " + parentDocument.getTitle(), 
                AuditLog.Severity.INFO);

        return savedVersion;
    }

    public void deleteDocument(Long id, User user) {
        Document document = documentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Document not found"));

        // Check permissions
        if (!hasPermission(document, user, DocumentPermission.PermissionType.DELETE)) {
            throw new RuntimeException("Insufficient permissions to delete document");
        }

        // Soft delete - change status to archived
        document.setStatus(Document.DocumentStatus.ARCHIVED);
        documentRepository.save(document);

        // Log audit event
        auditService.logSystemEvent("DOCUMENT_DELETED", 
                "Document deleted: " + document.getTitle() + " by " + user.getUsername(), 
                AuditLog.Severity.WARNING);
    }

    // Document access and permissions
    public boolean hasPermission(Document document, User user, DocumentPermission.PermissionType requiredPermission) {
        // Owner has all permissions
        if (document.getOwner().getId().equals(user.getId())) {
            return true;
        }

        // Check explicit permissions
        Optional<DocumentPermission> permission = permissionRepository.findActivePermission(document.getId(), user.getId());
        if (permission.isPresent() && permission.get().isValid()) {
            return permission.get().getPermissionType().includes(requiredPermission);
        }

        // Check role-based permissions
        return hasRoleBasedPermission(user, requiredPermission);
    }

    private boolean hasRoleBasedPermission(User user, DocumentPermission.PermissionType requiredPermission) {
        return user.getRoles().stream().anyMatch(role -> {
            switch (role.getName()) {
                case ROLE_ADMIN:
                    return true;
                case ROLE_QA:
                    return requiredPermission != DocumentPermission.PermissionType.DELETE;
                case ROLE_USER:
                    return requiredPermission == DocumentPermission.PermissionType.READ;
                default:
                    return false;
            }
        });
    }

    public void grantPermission(Long documentId, Long userId, DocumentPermission.PermissionType permissionType, User grantedBy) {
        Document document = documentRepository.findById(documentId)
                .orElseThrow(() -> new RuntimeException("Document not found"));
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Check if permission already exists
        Optional<DocumentPermission> existingPermission = permissionRepository.findByDocumentAndUser(document, user);
        if (existingPermission.isPresent()) {
            DocumentPermission permission = existingPermission.get();
            permission.setPermissionType(permissionType);
            permission.setIsActive(true);
            permission.setGrantedBy(grantedBy);
            permissionRepository.save(permission);
        } else {
            DocumentPermission permission = new DocumentPermission(document, user, permissionType, grantedBy);
            permissionRepository.save(permission);
        }

        // Log audit event
        auditService.logSystemEvent("DOCUMENT_PERMISSION_GRANTED", 
                "Permission " + permissionType + " granted to " + user.getUsername() + 
                " for document: " + document.getTitle(), 
                AuditLog.Severity.INFO);
    }

    // Document statistics and tracking
    public void incrementViewCount(Long documentId) {
        Document document = documentRepository.findById(documentId)
                .orElseThrow(() -> new RuntimeException("Document not found"));
        document.incrementViewCount();
        documentRepository.save(document);
    }

    public void incrementDownloadCount(Long documentId) {
        Document document = documentRepository.findById(documentId)
                .orElseThrow(() -> new RuntimeException("Document not found"));
        document.incrementDownloadCount();
        documentRepository.save(document);
    }

    // Search and filtering
    public Page<Document> searchDocuments(String title, String description, String fileName,
                                        Long categoryId, Long ownerId, Document.DocumentStatus status,
                                        Document.DocumentClassification classification,
                                        Boolean isCurrentVersion, Pageable pageable) {
        return documentRepository.findDocumentsWithFilters(title, description, fileName,
                categoryId, ownerId, status, classification, isCurrentVersion, pageable);
    }

    // Version management
    public List<Document> getDocumentVersions(Long parentDocumentId) {
        Document parentDocument = documentRepository.findById(parentDocumentId)
                .orElseThrow(() -> new RuntimeException("Document not found"));
        return documentRepository.findByParentDocumentOrderByVersionNumberDesc(parentDocument);
    }

    private Integer getNextVersionNumber(Document parentDocument) {
        List<Document> versions = documentRepository.findByParentDocumentOrderByVersionNumberDesc(parentDocument);
        if (versions.isEmpty()) {
            return parentDocument.getVersionNumber() + 1;
        }
        return versions.get(0).getVersionNumber() + 1;
    }

    // Utility methods
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf('.') == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    }

    private String calculateChecksum(byte[] data) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(data);
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Error calculating checksum", e);
        }
    }

    // Statistics methods - 增强错误处理
    public long getTotalDocumentCount() {
        try {
            return documentRepository.count();
        } catch (Exception e) {
            System.err.println("获取文档总数失败: " + e.getMessage());
            return 0L;
        }
    }

    public long getDocumentCountByStatus(Document.DocumentStatus status) {
        try {
            return documentRepository.countByStatus(status);
        } catch (Exception e) {
            System.err.println("获取文档状态统计失败: " + e.getMessage());
            return 0L;
        }
    }

    public long getPendingApprovalCount() {
        try {
            return documentRepository.countPendingApproval();
        } catch (Exception e) {
            System.err.println("获取待审批文档数失败: " + e.getMessage());
            return 0L;
        }
    }

    // 新增：获取文档统计详情
    public Map<String, Object> getDocumentStatistics() {
        Map<String, Object> stats = new HashMap<>();
        try {
            stats.put("totalDocuments", getTotalDocumentCount());
            stats.put("draftDocuments", getDocumentCountByStatus(Document.DocumentStatus.DRAFT));
            stats.put("underReviewDocuments", getDocumentCountByStatus(Document.DocumentStatus.UNDER_REVIEW));
            stats.put("approvedDocuments", getDocumentCountByStatus(Document.DocumentStatus.APPROVED));
            stats.put("publishedDocuments", getDocumentCountByStatus(Document.DocumentStatus.PUBLISHED));
            stats.put("archivedDocuments", getDocumentCountByStatus(Document.DocumentStatus.ARCHIVED));
            stats.put("pendingApproval", getPendingApprovalCount());

            // 按分类统计
            Map<String, Long> categoryStats = new HashMap<>();
            List<DocumentCategory> categories = categoryRepository.findAll();
            for (DocumentCategory category : categories) {
                long count = documentRepository.countByCategoryId(category.getId());
                categoryStats.put(category.getName(), count);
            }
            stats.put("categoryStats", categoryStats);

            return stats;
        } catch (Exception e) {
            System.err.println("获取文档统计失败: " + e.getMessage());
            return Map.of(
                "totalDocuments", 0L,
                "draftDocuments", 0L,
                "publishedDocuments", 0L,
                "pendingApproval", 0L,
                "error", e.getMessage()
            );
        }
    }

    public List<Document> getRecentDocuments(int limit) {
        return documentRepository.findRecentlyCreated(LocalDateTime.now().minusDays(7), 
                Pageable.ofSize(limit));
    }

    public List<Document> getMostDownloadedDocuments(int limit) {
        return documentRepository.findMostDownloaded(Pageable.ofSize(limit));
    }

    public Document restoreDocumentVersion(Long parentDocumentId, Long versionId, User user) {
        System.out.println("=== 恢复文档版本开始 ===");

        Document parentDocument = documentRepository.findById(parentDocumentId)
                .orElseThrow(() -> new RuntimeException("Parent document not found"));

        Document versionToRestore = documentRepository.findById(versionId)
                .orElseThrow(() -> new RuntimeException("Version document not found"));

        // 验证版本关系
        if (!versionToRestore.getParentDocument().getId().equals(parentDocumentId) &&
            !versionToRestore.getId().equals(parentDocumentId)) {
            throw new RuntimeException("Version does not belong to the specified document");
        }

        // 检查权限
        if (!hasPermission(parentDocument, user, DocumentPermission.PermissionType.WRITE)) {
            throw new RuntimeException("Insufficient permissions to restore document version");
        }

        // 将所有版本设置为非当前版本
        List<Document> allVersions = getDocumentVersions(parentDocumentId);
        for (Document version : allVersions) {
            version.setIsCurrentVersion(false);
            documentRepository.save(version);
        }

        // 设置要恢复的版本为当前版本
        versionToRestore.setIsCurrentVersion(true);
        versionToRestore.setStatus(Document.DocumentStatus.PUBLISHED);
        Document restoredVersion = documentRepository.save(versionToRestore);

        // 记录审计日志
        auditService.logSystemEvent("DOCUMENT_VERSION_RESTORED",
                "Document version " + versionToRestore.getVersionNumber() +
                " restored for: " + parentDocument.getTitle() + " by " + user.getUsername(),
                AuditLog.Severity.INFO);

        System.out.println("文档版本恢复成功: " + versionToRestore.getVersionNumber());
        return restoredVersion;
    }
}
