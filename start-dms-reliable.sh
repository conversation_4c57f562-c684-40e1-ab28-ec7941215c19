#!/bin/bash

# 制药DMS系统 - 可靠启动脚本
# 解决PostgreSQL频繁停止问题

echo "=========================================="
echo "🏥 制药文档管理系统 (Pharmaceutical DMS)"
echo "🚀 可靠启动脚本"
echo "=========================================="

# 设置变量
POSTGRES_BIN="/d/sql/pgsql/bin"
POSTGRES_DATA="/d/sql/pgsql/data"
PROJECT_DIR="/d/learn/java/dms"

# 切换到项目目录
cd "$PROJECT_DIR"
echo "📍 当前目录: $(pwd)"

# 函数：检查PostgreSQL状态
check_postgres() {
    if tasklist.exe | grep -q postgres.exe; then
        echo "✅ PostgreSQL正在运行"
        return 0
    else
        echo "❌ PostgreSQL未运行"
        return 1
    fi
}

# 函数：启动PostgreSQL
start_postgres() {
    echo "🗄️  启动PostgreSQL数据库..."
    
    # 停止可能存在的进程
    taskkill.exe //F //IM postgres.exe 2>/dev/null || true
    sleep 2
    
    # 启动PostgreSQL
    "$POSTGRES_BIN/pg_ctl" -D "$POSTGRES_DATA" start
    
    # 等待启动
    echo "⏳ 等待PostgreSQL启动..."
    for i in {1..15}; do
        sleep 2
        if check_postgres; then
            echo "✅ PostgreSQL启动成功"
            return 0
        fi
        echo "   等待中... ($i/15)"
    done
    
    echo "❌ PostgreSQL启动失败"
    return 1
}

# 函数：启动应用程序
start_application() {
    echo "🚀 启动DMS应用程序..."
    
    # 停止可能存在的Java进程
    taskkill.exe //F //IM java.exe 2>/dev/null || true
    sleep 2
    
    echo "📊 配置信息:"
    echo "   - 端口: 8081"
    echo "   - 数据库: PostgreSQL"
    echo "   - 访问地址: http://172.100.15.120:8081/dms/login"
    echo "   - 默认用户: admin / admin123"
    echo ""
    
    # 启动应用程序
    mvn spring-boot:run "-Dspring-boot.run.profiles=postgresql"
}

# 主启动流程
echo "🔄 第一步：启动PostgreSQL数据库"
if start_postgres; then
    echo ""
    echo "🔄 第二步：启动DMS应用程序"
    start_application
else
    echo ""
    echo "❌ PostgreSQL启动失败，尝试使用H2数据库..."
    echo "🔄 使用H2数据库启动应用程序"
    mvn spring-boot:run "-Dspring-boot.run.profiles=h2"
fi
