-- 制药DMS数据库初始化脚本
-- 创建数据库和基础数据

-- 1. 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS pharma_dms;

-- 2. 使用数据库
\c pharma_dms;

-- 3. 创建部门数据
INSERT INTO department (id, name, description, created_at, updated_at) VALUES 
(1, '质量保证部', 'Quality Assurance Department', NOW(), NOW()),
(2, '生产部', 'Production Department', NOW(), NOW()),
(3, '研发部', 'Research & Development Department', NOW(), NOW()),
(4, '法规事务部', 'Regulatory Affairs Department', NOW(), NOW()),
(5, '信息技术部', 'Information Technology Department', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 4. 创建文档分类
INSERT INTO document_category (id, name, description, created_at, updated_at) VALUES 
(1, '标准操作程序', 'Standard Operating Procedures', NOW(), NOW()),
(2, '技术文档', 'Technical Documents', NOW(), NOW()),
(3, '培训材料', 'Training Materials', NOW(), NOW()),
(4, '质量记录', 'Quality Records', NOW(), NOW()),
(5, '法规文件', 'Regulatory Documents', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 5. 创建默认管理员用户
INSERT INTO users (id, username, email, password, first_name, last_name, is_active, is_locked, department_id, created_at, updated_at) VALUES 
(1, 'admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxIcnvtcflQjXaC', 'System', 'Administrator', true, false, 5, NOW(), NOW()),
(2, 'qa_manager', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxIcnvtcflQjXaC', 'QA', 'Manager', true, false, 1, NOW(), NOW()),
(3, 'operator1', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lbdxIcnvtcflQjXaC', 'Production', 'Operator', true, false, 2, NOW(), NOW())
ON CONFLICT (username) DO NOTHING;

-- 6. 创建用户角色
INSERT INTO user_roles (user_id, role) VALUES 
(1, 'ADMIN'),
(1, 'USER'),
(2, 'QA'),
(2, 'USER'),
(3, 'USER')
ON CONFLICT DO NOTHING;

-- 7. 创建示例培训课程
INSERT INTO training_courses (id, course_code, title, description, course_type, status, duration_minutes, passing_score, max_attempts, is_mandatory, instructor_id, created_at, updated_at) VALUES
(1, 'GMP-001', 'GMP基础培训', 'Good Manufacturing Practice基础知识培训', 'GMP', 'ACTIVE', 120, 80, 3, true, 2, NOW(), NOW()),
(2, 'SOP-001', 'SOP操作培训', '标准操作程序培训', 'SOP', 'ACTIVE', 90, 85, 3, true, 2, NOW(), NOW()),
(3, 'SAFETY-001', '安全培训', '生产安全培训', 'SAFETY', 'ACTIVE', 60, 80, 3, true, 2, NOW(), NOW()),
(4, 'QUALITY-001', '质量管理培训', '质量管理体系培训', 'QUALITY', 'DRAFT', 150, 85, 3, false, 2, NOW(), NOW())
ON CONFLICT (course_code) DO NOTHING;

-- 8. 重置序列
SELECT setval('department_id_seq', (SELECT MAX(id) FROM department));
SELECT setval('document_category_id_seq', (SELECT MAX(id) FROM document_category));
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));
SELECT setval('training_courses_id_seq', (SELECT MAX(id) FROM training_courses));

-- 9. 显示统计信息
SELECT 'Users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'Departments', COUNT(*) FROM department
UNION ALL
SELECT 'Document Categories', COUNT(*) FROM document_category
UNION ALL
SELECT 'Training Courses', COUNT(*) FROM training_courses;
