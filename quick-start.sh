#!/bin/bash

# 制药DMS快速启动脚本
# 最简化的启动命令

echo "🚀 快速启动制药DMS系统..."

# 切换到应用目录
cd /d/learn/java/dms

# 检查并启动PostgreSQL
echo "🗄️  检查PostgreSQL数据库..."
if ! tasklist | grep -q postgres.exe; then
    echo "启动PostgreSQL数据库..."
    /d/sql/pgsql/bin/pg_ctl -D /d/sql/pgsql/data start
    echo "等待数据库启动..."
    sleep 5
else
    echo "PostgreSQL已在运行"
fi

# 停止可能运行的旧进程
echo "🔄 停止旧的应用程序进程..."
taskkill //F //IM java.exe 2>/dev/null || true

# 启动应用程序
echo "🚀 启动DMS应用程序..."
echo "配置信息:"
echo "  - 端口: 8081"
echo "  - 数据库: PostgreSQL"
echo "  - 访问地址: http://**************:8081/dms/login"

# 使用JAR文件启动
java -jar target/dms-1.0.0.jar \
    --spring.profiles.active=postgresql \
    --server.address=0.0.0.0 \
    --server.port=8081 \
    --logging.level.com.pharma.dms=INFO \
    --logging.level.org.springframework.boot=INFO
