package com.pharma.dms.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Controller
public class WebController {

    @GetMapping("/")
    public String home() {
        return "redirect:/login";
    }

    @GetMapping("/login")
    public String login() {
        return "login";
    }

    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        model.addAttribute("pageTitle", "Dashboard");
        return "dashboard";
    }

    @GetMapping("/users")
    public String users(Model model) {
        model.addAttribute("pageTitle", "User Management");
        return "users";
    }

    @GetMapping("/profile")
    public String profile(Model model) {
        model.addAttribute("pageTitle", "User Profile");
        return "profile";
    }

    @GetMapping("/documents")
    public String documents(Model model) {
        model.addAttribute("pageTitle", "Document Management");
        return "documents";
    }

    @GetMapping("/system-overview")
    public String systemOverview(Model model) {
        model.addAttribute("pageTitle", "System Overview");
        return "system-overview";
    }

    @GetMapping("/my-training")
    public String myTraining(Model model) {
        model.addAttribute("pageTitle", "My Training");
        return "my-training";
    }

    @GetMapping("/training-courses")
    public String trainingCourses(Model model) {
        model.addAttribute("pageTitle", "Training Courses");
        return "training-courses";
    }

    @GetMapping("/training-management")
    public String trainingManagement(Model model) {
        model.addAttribute("pageTitle", "Training Management");
        return "training-management";
    }

    @GetMapping("/training-assignment")
    public String trainingAssignment(Model model) {
        model.addAttribute("pageTitle", "Training Assignment");
        return "training-assignment";
    }

    @GetMapping("/reports")
    public String reports(Model model) {
        model.addAttribute("pageTitle", "Reports");
        return "reports";
    }

    @GetMapping("/training/course/{courseId}/start")
    public String startTrainingCourse(@PathVariable Long courseId, Model model) {
        model.addAttribute("pageTitle", "开始培训");
        model.addAttribute("courseId", courseId);
        return "training-start";
    }

    @GetMapping("/training/assignment/{assignmentId}/start")
    public String startTrainingAssignment(@PathVariable Long assignmentId, Model model) {
        model.addAttribute("pageTitle", "开始培训");
        model.addAttribute("assignmentId", assignmentId);
        return "training-start";
    }

    @GetMapping("/course/{courseId}/details")
    public String courseDetails(@PathVariable Long courseId, Model model) {
        model.addAttribute("pageTitle", "课程详情");
        model.addAttribute("courseId", courseId);
        return "course-details";
    }

    @GetMapping("/settings")
    public String settings(Model model) {
        model.addAttribute("pageTitle", "Settings");
        return "settings";
    }

    @GetMapping("/ai-test")
    @PreAuthorize("hasRole('USER')")
    public String aiTest(Model model) {
        model.addAttribute("pageTitle", "AI功能测试");
        return "ai-test";
    }

    @GetMapping("/upload-test")
    @PreAuthorize("hasRole('USER')")
    public String uploadTest(Model model) {
        model.addAttribute("pageTitle", "文档上传测试");
        return "upload-test";
    }

    @GetMapping("/auth-test")
    public String authTest(Model model) {
        model.addAttribute("pageTitle", "Auth Test");
        return "auth-test";
    }

    @GetMapping("/token-test")
    public String tokenTest(Model model) {
        model.addAttribute("pageTitle", "Token Test");
        return "token-test";
    }

    @GetMapping("/debug-auth")
    public String debugAuth(Model model) {
        model.addAttribute("pageTitle", "Debug Auth");
        return "debug-auth";
    }

    @GetMapping("/system-status")
    public String systemStatus(Model model) {
        model.addAttribute("pageTitle", "System Status");
        return "system-status";
    }

    @GetMapping("/delete-test")
    public String deleteTest(Model model) {
        model.addAttribute("pageTitle", "Delete Test");
        return "delete-test";
    }

    @GetMapping("/approval-management")
    public String approvalManagement(Model model) {
        model.addAttribute("pageTitle", "Approval Management");
        return "approval-management";
    }

    @GetMapping("/approval-workflow")
    public String approvalWorkflow(Model model) {
        model.addAttribute("pageTitle", "Approval Workflow");
        return "approval-workflow";
    }
}
