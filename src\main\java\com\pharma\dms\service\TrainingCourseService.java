package com.pharma.dms.service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pharma.dms.entity.AuditLog;
import com.pharma.dms.entity.Department;
import com.pharma.dms.entity.TrainingAssignment;
import com.pharma.dms.entity.TrainingCourse;
import com.pharma.dms.entity.User;
import com.pharma.dms.repository.DepartmentRepository;
import com.pharma.dms.repository.RoleRepository;
import com.pharma.dms.repository.TrainingAssignmentRepository;
import com.pharma.dms.repository.TrainingCourseRepository;
import com.pharma.dms.repository.TrainingRecordRepository;
import com.pharma.dms.repository.UserRepository;

@Service
@Transactional
public class TrainingCourseService {

    @Autowired
    private TrainingCourseRepository courseRepository;

    @Autowired
    private TrainingAssignmentRepository assignmentRepository;

    @Autowired
    private TrainingRecordRepository recordRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private AuditService auditService;

    // Course CRUD operations
    public List<TrainingCourse> getAllCourses() {
        return courseRepository.findAll();
    }

    public Page<TrainingCourse> getAllCourses(Pageable pageable) {
        return courseRepository.findAll(pageable);
    }

    public Optional<TrainingCourse> getCourseById(Long id) {
        return courseRepository.findById(id);
    }

    public Optional<TrainingCourse> getCourseByCourseCode(String courseCode) {
        return courseRepository.findByCourseCode(courseCode);
    }

    public List<TrainingCourse> getActiveCourses() {
        return courseRepository.findActiveCourses(LocalDateTime.now());
    }

    public List<TrainingCourse> getCoursesByStatus(TrainingCourse.CourseStatus status) {
        return courseRepository.findByStatus(status);
    }

    public List<TrainingCourse> getCoursesByType(TrainingCourse.CourseType courseType) {
        return courseRepository.findByCourseType(courseType);
    }

    public List<TrainingCourse> getMandatoryCourses() {
        return courseRepository.findByIsMandatoryTrue();
    }

    // Course creation and management
    public TrainingCourse createCourse(TrainingCourse course) {
        if (courseRepository.existsByCourseCode(course.getCourseCode())) {
            throw new RuntimeException("Course code already exists: " + course.getCourseCode());
        }

        TrainingCourse savedCourse = courseRepository.save(course);

        // Log audit event
        auditService.logSystemEvent("TRAINING_COURSE_CREATED", 
                "Training course created: " + course.getTitle(), 
                AuditLog.Severity.INFO);

        return savedCourse;
    }

    public TrainingCourse updateCourse(Long id, TrainingCourse courseDetails) {
        TrainingCourse course = courseRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Course not found"));

        String oldValues = course.toString();

        course.setTitle(courseDetails.getTitle());
        course.setDescription(courseDetails.getDescription());
        course.setCourseType(courseDetails.getCourseType());
        course.setDurationMinutes(courseDetails.getDurationMinutes());
        course.setPassingScore(courseDetails.getPassingScore());
        course.setMaxAttempts(courseDetails.getMaxAttempts());
        course.setIsMandatory(courseDetails.getIsMandatory());
        course.setValidityPeriodMonths(courseDetails.getValidityPeriodMonths());
        course.setAutoAssign(courseDetails.getAutoAssign());
        course.setLearningObjectives(courseDetails.getLearningObjectives());
        course.setPrerequisites(courseDetails.getPrerequisites());
        course.setEffectiveDate(courseDetails.getEffectiveDate());
        course.setExpiryDate(courseDetails.getExpiryDate());
        course.setInstructor(courseDetails.getInstructor());

        TrainingCourse updatedCourse = courseRepository.save(course);

        // Log audit event
        auditService.logSystemEvent("TRAINING_COURSE_UPDATED", 
                "Training course updated: " + course.getTitle(), 
                AuditLog.Severity.INFO);

        return updatedCourse;
    }

    public void deleteCourse(Long id) {
        TrainingCourse course = courseRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Course not found"));

        // Check if course has active assignments
        long activeAssignments = assignmentRepository.countByCourse(course);
        if (activeAssignments > 0) {
            throw new RuntimeException("Cannot delete course with active assignments");
        }

        // Soft delete - change status to archived
        course.setStatus(TrainingCourse.CourseStatus.ARCHIVED);
        courseRepository.save(course);

        // Log audit event
        auditService.logSystemEvent("TRAINING_COURSE_DELETED", 
                "Training course archived: " + course.getTitle(), 
                AuditLog.Severity.WARNING);
    }

    // Course approval workflow
    public TrainingCourse approveCourse(Long id, User approver) {
        TrainingCourse course = courseRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Course not found"));

        if (course.getStatus() != TrainingCourse.CourseStatus.UNDER_REVIEW) {
            throw new RuntimeException("Course is not under review");
        }

        course.setStatus(TrainingCourse.CourseStatus.APPROVED);
        course.setApprover(approver);
        course.setApprovalDate(LocalDateTime.now());

        TrainingCourse approvedCourse = courseRepository.save(course);

        // Log audit event
        auditService.logSystemEvent("TRAINING_COURSE_APPROVED", 
                "Training course approved: " + course.getTitle() + " by " + approver.getUsername(), 
                AuditLog.Severity.INFO);

        return approvedCourse;
    }

    public TrainingCourse activateCourse(Long id) {
        TrainingCourse course = courseRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Course not found"));

        if (course.getStatus() != TrainingCourse.CourseStatus.APPROVED) {
            throw new RuntimeException("Course must be approved before activation");
        }

        course.setStatus(TrainingCourse.CourseStatus.ACTIVE);
        TrainingCourse activatedCourse = courseRepository.save(course);

        // Auto-assign if enabled
        if (course.getAutoAssign()) {
            autoAssignCourse(course);
        }

        // Log audit event
        auditService.logSystemEvent("TRAINING_COURSE_ACTIVATED", 
                "Training course activated: " + course.getTitle(), 
                AuditLog.Severity.INFO);

        return activatedCourse;
    }

    // Course assignment
    public void assignCourseToUser(Long courseId, Long userId, User assignedBy) {
        TrainingCourse course = courseRepository.findById(courseId)
                .orElseThrow(() -> new RuntimeException("Course not found"));
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Check if already assigned
        Optional<TrainingAssignment> existingAssignment = assignmentRepository.findByCourseAndUser(course, user);
        if (existingAssignment.isPresent()) {
            throw new RuntimeException("User is already assigned to this course");
        }

        TrainingAssignment assignment = new TrainingAssignment(course, user, assignedBy);
        assignment.setIsMandatory(course.getIsMandatory());
        
        // Set due date if course has validity period
        if (course.getValidityPeriodMonths() != null) {
            assignment.setDueDate(LocalDateTime.now().plusMonths(course.getValidityPeriodMonths()));
        }

        assignmentRepository.save(assignment);

        // Log audit event
        auditService.logSystemEvent("TRAINING_ASSIGNED", 
                "Training assigned: " + course.getTitle() + " to " + user.getUsername(), 
                AuditLog.Severity.INFO);
    }

    public void assignCourseToDepartment(Long courseId, Long departmentId, User assignedBy) {
        TrainingCourse course = courseRepository.findById(courseId)
                .orElseThrow(() -> new RuntimeException("Course not found"));
        Department department = departmentRepository.findById(departmentId)
                .orElseThrow(() -> new RuntimeException("Department not found"));

        List<User> departmentUsers = userRepository.findByDepartmentAndIsActiveTrue(department);
        
        for (User user : departmentUsers) {
            try {
                assignCourseToUser(courseId, user.getId(), assignedBy);
            } catch (RuntimeException e) {
                // Skip if already assigned
                if (!e.getMessage().contains("already assigned")) {
                    throw e;
                }
            }
        }

        // Log audit event
        auditService.logSystemEvent("TRAINING_ASSIGNED_DEPARTMENT", 
                "Training assigned to department: " + course.getTitle() + " to " + department.getName(), 
                AuditLog.Severity.INFO);
    }

    // Auto-assignment logic
    private void autoAssignCourse(TrainingCourse course) {
        List<User> eligibleUsers = findEligibleUsersForCourse(course);
        
        for (User user : eligibleUsers) {
            try {
                assignCourseToUser(course.getId(), user.getId(), null); // System assignment
            } catch (RuntimeException e) {
                // Skip if already assigned
            }
        }
    }

    private List<User> findEligibleUsersForCourse(TrainingCourse course) {
        if (course.getTargetDepartments().isEmpty() && course.getTargetRoles().isEmpty()) {
            // If no specific targets, assign to all active users
            return userRepository.findByIsActiveTrue();
        }

        List<User> eligibleUsers = userRepository.findByIsActiveTrue();
        
        // Filter by department if specified
        if (!course.getTargetDepartments().isEmpty()) {
            eligibleUsers = eligibleUsers.stream()
                    .filter(user -> user.getDepartment() != null && 
                           course.getTargetDepartments().contains(user.getDepartment()))
                    .toList();
        }

        // Filter by role if specified
        if (!course.getTargetRoles().isEmpty()) {
            eligibleUsers = eligibleUsers.stream()
                    .filter(user -> user.getRoles().stream()
                           .anyMatch(role -> course.getTargetRoles().contains(role)))
                    .toList();
        }

        return eligibleUsers;
    }

    // Search and filtering
    public Page<TrainingCourse> searchCourses(String title, String courseCode, 
                                             TrainingCourse.CourseType courseType,
                                             TrainingCourse.CourseStatus status,
                                             Long instructorId, Boolean isMandatory,
                                             Pageable pageable) {
        return courseRepository.findCoursesWithFilters(title, courseCode, courseType, 
                                                      status, instructorId, isMandatory, pageable);
    }

    // User-specific queries
    public List<TrainingCourse> getAvailableCoursesForUser(User user) {
        return courseRepository.findAvailableCoursesForUser(user.getDepartment(), 
                                                           user.getRoles().stream().toList(), 
                                                           LocalDateTime.now());
    }

    // Statistics - 增强错误处理
    public long getTotalCourseCount() {
        try {
            return courseRepository.count();
        } catch (Exception e) {
            System.err.println("获取课程总数失败: " + e.getMessage());
            return 0L;
        }
    }

    public long getCourseCountByStatus(TrainingCourse.CourseStatus status) {
        try {
            return courseRepository.countByStatus(status);
        } catch (Exception e) {
            System.err.println("获取课程状态统计失败: " + e.getMessage());
            return 0L;
        }
    }

    public long getMandatoryCourseCount() {
        try {
            return courseRepository.countMandatoryCourses();
        } catch (Exception e) {
            System.err.println("获取必修课程数失败: " + e.getMessage());
            return 0L;
        }
    }

    public List<TrainingCourse> getCoursesAwaitingApproval() {
        try {
            return courseRepository.findCoursesAwaitingApproval();
        } catch (Exception e) {
            System.err.println("获取待审批课程失败: " + e.getMessage());
            return List.of();
        }
    }

    // 新增：获取培训课程统计详情
    public Map<String, Object> getCourseStatistics() {
        Map<String, Object> stats = new HashMap<>();
        try {
            stats.put("totalCourses", getTotalCourseCount());
            stats.put("activeCourses", getCourseCountByStatus(TrainingCourse.CourseStatus.ACTIVE));
            stats.put("draftCourses", getCourseCountByStatus(TrainingCourse.CourseStatus.DRAFT));
            stats.put("approvedCourses", getCourseCountByStatus(TrainingCourse.CourseStatus.APPROVED));
            stats.put("archivedCourses", getCourseCountByStatus(TrainingCourse.CourseStatus.ARCHIVED));
            stats.put("mandatoryCourses", getMandatoryCourseCount());
            stats.put("awaitingApproval", getCoursesAwaitingApproval().size());

            // 按课程类型统计
            Map<String, Long> typeStats = new HashMap<>();
            for (TrainingCourse.CourseType type : TrainingCourse.CourseType.values()) {
                long count = courseRepository.countByCourseType(type);
                typeStats.put(type.name(), count);
            }
            stats.put("courseTypeStats", typeStats);

            return stats;
        } catch (Exception e) {
            System.err.println("获取培训课程统计失败: " + e.getMessage());
            return Map.of(
                "totalCourses", 0L,
                "activeCourses", 0L,
                "draftCourses", 0L,
                "mandatoryCourses", 0L,
                "awaitingApproval", 0L,
                "error", e.getMessage()
            );
        }
    }

    public List<TrainingCourse> getExpiringCourses(int days) {
        LocalDateTime startDate = LocalDateTime.now();
        LocalDateTime endDate = startDate.plusDays(days);
        return courseRepository.findCoursesExpiringBetween(startDate, endDate);
    }

    public List<TrainingCourse> getRecentCourses(int limit) {
        return courseRepository.findRecentlyCreated(LocalDateTime.now().minusDays(7), 
                org.springframework.data.domain.Pageable.ofSize(limit));
    }

    public List<TrainingCourse> getPopularCourses(int limit) {
        return courseRepository.findMostPopularCourses(
                org.springframework.data.domain.Pageable.ofSize(limit));
    }
}
