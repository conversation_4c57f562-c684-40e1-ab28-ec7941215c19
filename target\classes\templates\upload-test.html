<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试 - 制药DMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-upload"></i> 文件上传测试</h3>
                    </div>
                    <div class="card-body">
                        <!-- 上传表单 -->
                        <form id="uploadForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="file" class="form-label">选择文件</label>
                                <input type="file" class="form-control" id="file" name="file" required>
                            </div>
                            <div class="mb-3">
                                <label for="title" class="form-label">文档标题</label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">描述（可选）</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i> 上传文件
                            </button>
                        </form>

                        <!-- 进度条 -->
                        <div id="progressContainer" class="mt-3" style="display: none;">
                            <div class="progress">
                                <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>

                        <!-- 结果显示 -->
                        <div id="result" class="mt-3"></div>

                        <!-- 调试信息 -->
                        <div class="mt-4">
                            <h5>调试信息</h5>
                            <button type="button" class="btn btn-info btn-sm" onclick="testUploadDebug()">
                                <i class="fas fa-bug"></i> 测试上传调试
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="checkAuth()">
                                <i class="fas fa-user"></i> 检查认证
                            </button>
                        </div>
                        <div id="debugInfo" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script th:src="@{/js/auth-utils.js}"></script>
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.getElementById('file');
            const titleInput = document.getElementById('title');
            const descriptionInput = document.getElementById('description');
            
            if (!fileInput.files[0]) {
                showResult('请选择文件', 'danger');
                return;
            }
            
            formData.append('file', fileInput.files[0]);
            formData.append('title', titleInput.value);
            formData.append('description', descriptionInput.value);
            
            try {
                showProgress(true);
                showResult('正在上传文件...', 'info');
                
                console.log('开始上传文件:', fileInput.files[0].name);
                
                const response = await authUtils.secureApiCall('/dms/api/documents/upload', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('上传响应状态:', response.status);
                const result = await response.json();
                console.log('上传响应结果:', result);
                
                if (response.ok) {
                    showResult('文件上传成功！文档ID: ' + result.data.id, 'success');
                    document.getElementById('uploadForm').reset();
                } else {
                    showResult('上传失败: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('上传错误:', error);
                showResult('上传失败: ' + error.message, 'danger');
            } finally {
                showProgress(false);
            }
        });
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        }
        
        function showProgress(show) {
            const progressContainer = document.getElementById('progressContainer');
            if (show) {
                progressContainer.style.display = 'block';
                document.getElementById('progressBar').style.width = '100%';
            } else {
                progressContainer.style.display = 'none';
                document.getElementById('progressBar').style.width = '0%';
            }
        }
        
        async function testUploadDebug() {
            try {
                const formData = new FormData();
                formData.append('file', new Blob(['test content'], {type: 'text/plain'}), 'test.txt');
                formData.append('title', 'Debug Test');
                
                const response = await authUtils.secureApiCall('/dms/api/test/upload-simple-debug', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                document.getElementById('debugInfo').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('debugInfo').innerHTML = 
                    '<div class="alert alert-danger">调试测试失败: ' + error.message + '</div>';
            }
        }
        
        async function checkAuth() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/test/user-me-debug');
                const result = await response.json();
                document.getElementById('debugInfo').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('debugInfo').innerHTML = 
                    '<div class="alert alert-danger">认证检查失败: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
