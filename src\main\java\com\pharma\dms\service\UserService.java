package com.pharma.dms.service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pharma.dms.dto.PasswordChangeRequest;
import com.pharma.dms.dto.SignupRequest;
import com.pharma.dms.dto.UserUpdateRequest;
import com.pharma.dms.entity.Department;
import com.pharma.dms.entity.Role;
import com.pharma.dms.entity.User;
import com.pharma.dms.repository.DepartmentRepository;
import com.pharma.dms.repository.RoleRepository;
import com.pharma.dms.repository.UserRepository;

@Service
@Transactional
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private AuditService auditService;

    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    public Page<User> getAllUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    public Optional<User> getUserById(Long id) {
        return userRepository.findById(id);
    }

    public Optional<User> getUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    public List<User> getActiveUsers() {
        return userRepository.findByIsActiveTrue();
    }

    public List<User> getUsersByDepartment(Long departmentId) {
        return userRepository.findByDepartmentId(departmentId);
    }

    public User createUser(SignupRequest signupRequest) {
        // Validate username and email uniqueness
        if (userRepository.existsByUsername(signupRequest.getUsername())) {
            throw new RuntimeException("Username is already taken!");
        }

        if (userRepository.existsByEmail(signupRequest.getEmail())) {
            throw new RuntimeException("Email is already in use!");
        }

        // Create new user
        User user = new User();
        user.setUsername(signupRequest.getUsername());
        user.setEmail(signupRequest.getEmail());
        user.setPassword(passwordEncoder.encode(signupRequest.getPassword()));
        user.setFirstName(signupRequest.getFirstName());
        user.setLastName(signupRequest.getLastName());
        user.setPhone(signupRequest.getPhone());
        user.setIsActive(true);
        user.setPasswordChangeRequired(true); // New users must change password

        // Set department if provided
        if (signupRequest.getDepartmentId() != null) {
            Department department = departmentRepository.findById(signupRequest.getDepartmentId())
                    .orElseThrow(() -> new RuntimeException("Department not found"));
            user.setDepartment(department);
        }

        // Set roles
        Set<String> strRoles = signupRequest.getRole();
        Set<Role> roles = new HashSet<>();

        if (strRoles == null || strRoles.isEmpty()) {
            Role userRole = roleRepository.findByName(Role.RoleName.ROLE_USER)
                    .orElseThrow(() -> new RuntimeException("Default role not found"));
            roles.add(userRole);
        } else {
            strRoles.forEach(role -> {
                switch (role.toLowerCase()) {
                    case "admin":
                        Role adminRole = roleRepository.findByName(Role.RoleName.ROLE_ADMIN)
                                .orElseThrow(() -> new RuntimeException("Admin role not found"));
                        roles.add(adminRole);
                        break;
                    case "qa":
                        Role qaRole = roleRepository.findByName(Role.RoleName.ROLE_QA)
                                .orElseThrow(() -> new RuntimeException("QA role not found"));
                        roles.add(qaRole);
                        break;
                    default:
                        Role userRole = roleRepository.findByName(Role.RoleName.ROLE_USER)
                                .orElseThrow(() -> new RuntimeException("User role not found"));
                        roles.add(userRole);
                }
            });
        }

        user.setRoles(roles);
        User savedUser = userRepository.save(user);

        // Log audit event
        auditService.logUserCreation(savedUser);

        return savedUser;
    }

    public User updateUser(Long id, User userDetails) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found"));

        String oldValues = user.toString(); // For audit

        user.setEmail(userDetails.getEmail());
        user.setFirstName(userDetails.getFirstName());
        user.setLastName(userDetails.getLastName());
        user.setPhone(userDetails.getPhone());

        if (userDetails.getDepartment() != null) {
            user.setDepartment(userDetails.getDepartment());
        }

        User updatedUser = userRepository.save(user);

        // Log audit event
        auditService.logUserUpdate(updatedUser, oldValues, updatedUser.toString());

        return updatedUser;
    }

    public User updateUserProfile(Long id, UserUpdateRequest userUpdateRequest) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found"));

        String oldValues = user.toString(); // For audit

        user.setEmail(userUpdateRequest.getEmail());
        user.setFirstName(userUpdateRequest.getFirstName());
        user.setLastName(userUpdateRequest.getLastName());
        user.setPhone(userUpdateRequest.getPhone());

        if (userUpdateRequest.getDepartmentId() != null) {
            Department department = departmentRepository.findById(userUpdateRequest.getDepartmentId())
                    .orElseThrow(() -> new RuntimeException("Department not found"));
            user.setDepartment(department);
        }

        if (userUpdateRequest.getIsActive() != null) {
            user.setIsActive(userUpdateRequest.getIsActive());
        }

        User updatedUser = userRepository.save(user);

        // Log audit event
        auditService.logUserUpdate(updatedUser, oldValues, updatedUser.toString());

        return updatedUser;
    }

    public void deleteUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Soft delete - just deactivate
        user.setIsActive(false);
        userRepository.save(user);

        // Log audit event
        auditService.logUserDeletion(user);
    }

    public void activateUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found"));

        user.setIsActive(true);
        userRepository.save(user);

        // Log audit event
        auditService.logUserActivation(user);
    }

    public void lockUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found"));

        user.setIsLocked(true);
        userRepository.save(user);

        // Log audit event
        auditService.logUserLock(user);
    }

    public void unlockUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found"));

        user.setIsLocked(false);
        user.setFailedLoginAttempts(0);
        userRepository.save(user);

        // Log audit event
        auditService.logUserUnlock(user);
    }

    public void changePassword(Long id, String newPassword) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found"));

        user.setPassword(passwordEncoder.encode(newPassword));
        user.setPasswordChangeRequired(false);
        userRepository.save(user);

        // Log audit event
        auditService.logPasswordChange(user);
    }

    public void changePassword(Long userId, PasswordChangeRequest passwordChangeRequest) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Verify current password
        if (!passwordEncoder.matches(passwordChangeRequest.getCurrentPassword(), user.getPassword())) {
            throw new RuntimeException("Current password is incorrect");
        }

        // Verify new passwords match
        if (!passwordChangeRequest.isPasswordsMatch()) {
            throw new RuntimeException("New passwords do not match");
        }

        // Update password
        user.setPassword(passwordEncoder.encode(passwordChangeRequest.getNewPassword()));
        user.setPasswordChangeRequired(false);
        userRepository.save(user);

        // Log audit event
        auditService.logPasswordChange(user);
    }

    public void updateLastLogin(String username) {
        Optional<User> userOpt = userRepository.findByUsername(username);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setLastLogin(LocalDateTime.now());
            user.setFailedLoginAttempts(0);
            userRepository.save(user);
        }
    }

    public void incrementFailedLoginAttempts(String username) {
        Optional<User> userOpt = userRepository.findByUsername(username);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setFailedLoginAttempts(user.getFailedLoginAttempts() + 1);
            
            // Lock user after 5 failed attempts
            if (user.getFailedLoginAttempts() >= 5) {
                user.setIsLocked(true);
                auditService.logUserAutoLock(user);
            }
            
            userRepository.save(user);
        }
    }

    public Page<User> searchUsers(String username, String email, String firstName, 
                                 String lastName, Boolean isActive, Long departmentId, 
                                 Pageable pageable) {
        return userRepository.findUsersWithFilters(username, email, firstName, 
                                                  lastName, isActive, departmentId, pageable);
    }

    public long getActiveUserCount() {
        try {
            return userRepository.countActiveUsers();
        } catch (Exception e) {
            System.err.println("获取活跃用户数失败: " + e.getMessage());
            return 0L;
        }
    }

    public long getLockedUserCount() {
        try {
            return userRepository.countLockedUsers();
        } catch (Exception e) {
            System.err.println("获取锁定用户数失败: " + e.getMessage());
            return 0L;
        }
    }

    public long getTotalUserCount() {
        try {
            return userRepository.countTotalUsers();
        } catch (Exception e) {
            System.err.println("获取总用户数失败: " + e.getMessage());
            return 0L;
        }
    }

    public long getRecentlyActiveUserCount() {
        try {
            // 最近24小时内登录的用户
            LocalDateTime since = LocalDateTime.now().minusHours(24);
            return userRepository.countRecentlyActiveUsers(since);
        } catch (Exception e) {
            System.err.println("获取最近活跃用户数失败: " + e.getMessage());
            return 0L;
        }
    }

    // 新增：获取用户统计详情
    public Map<String, Object> getUserStatistics() {
        Map<String, Object> stats = new HashMap<>();
        try {
            stats.put("totalUsers", getTotalUserCount());
            stats.put("activeUsers", getActiveUserCount());
            stats.put("lockedUsers", getLockedUserCount());
            stats.put("recentlyActiveUsers", getRecentlyActiveUserCount());

            // 按部门统计
            List<Department> departments = departmentRepository.findAll();
            Map<String, Long> departmentStats = new HashMap<>();
            for (Department dept : departments) {
                long count = userRepository.countByDepartmentAndIsActiveTrue(dept);
                departmentStats.put(dept.getName(), count);
            }
            stats.put("departmentStats", departmentStats);

            return stats;
        } catch (Exception e) {
            System.err.println("获取用户统计失败: " + e.getMessage());
            return Map.of(
                "totalUsers", 0L,
                "activeUsers", 0L,
                "lockedUsers", 0L,
                "recentlyActiveUsers", 0L,
                "error", e.getMessage()
            );
        }
    }

    public List<User> getAdminUsers() {
        // 获取所有活跃的管理员用户
        return userRepository.findByIsActiveTrue().stream()
                .filter(user -> user.getRoles().stream()
                        .anyMatch(role -> role.getName() == Role.RoleName.ROLE_ADMIN))
                .toList();
    }
}
