<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>培训分配 - 制药文档管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .user-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .user-card:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.15);
        }
        .user-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #007bff, #6610f2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .assignment-summary {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/training-courses">
                                <i class="fas fa-graduation-cap me-2"></i>培训课程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/training-assignment">
                                <i class="fas fa-user-plus me-2"></i>培训分配
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/my-training">
                                <i class="fas fa-book-reader me-2"></i>我的培训
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">培训分配</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button class="btn btn-primary" onclick="showAssignmentModal()">
                            <i class="fas fa-plus me-2"></i>新建分配
                        </button>
                    </div>
                </div>

                <!-- 分配摘要 -->
                <div class="assignment-summary" id="assignmentSummary" style="display: none;">
                    <h5><i class="fas fa-info-circle me-2"></i>分配摘要</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <strong>课程：</strong><span id="selectedCourse">-</span>
                        </div>
                        <div class="col-md-3">
                            <strong>选中人员：</strong><span id="selectedCount">0</span> 人
                        </div>
                        <div class="col-md-3">
                            <strong>截止日期：</strong><span id="selectedDueDate">-</span>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-success btn-sm" onclick="executeAssignment()">
                                <i class="fas fa-check me-1"></i>执行分配
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 筛选区域 -->
                <div class="filter-section">
                    <h5><i class="fas fa-filter me-2"></i>人员筛选</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">部门</label>
                            <select class="form-select" id="departmentFilter" onchange="filterUsers()">
                                <option value="">所有部门</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">角色</label>
                            <select class="form-select" id="roleFilter" onchange="filterUsers()">
                                <option value="">所有角色</option>
                                <option value="ADMIN">管理员</option>
                                <option value="QA">质量保证</option>
                                <option value="USER">普通用户</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">状态</label>
                            <select class="form-select" id="statusFilter" onchange="filterUsers()">
                                <option value="">所有状态</option>
                                <option value="active">活跃</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">搜索</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="姓名或用户名" onkeyup="filterUsers()">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button class="btn btn-outline-primary btn-sm me-2" onclick="selectAll()">
                                <i class="fas fa-check-square me-1"></i>全选
                            </button>
                            <button class="btn btn-outline-secondary btn-sm me-2" onclick="selectNone()">
                                <i class="fas fa-square me-1"></i>取消全选
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="selectByDepartment()">
                                <i class="fas fa-building me-1"></i>按部门选择
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 用户列表 -->
                <div class="row" id="usersList">
                    <!-- 用户卡片将在这里动态生成 -->
                </div>

                <!-- 分页 -->
                <nav aria-label="用户分页" class="mt-4">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮将在这里动态生成 -->
                    </ul>
                </nav>
            </main>
        </div>
    </div>

    <!-- 分配模态框 -->
    <div class="modal fade" id="assignmentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">新建培训分配</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="assignmentForm">
                        <div class="mb-3">
                            <label class="form-label">选择课程 *</label>
                            <select class="form-select" id="courseSelect" required>
                                <option value="">请选择课程</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">截止日期</label>
                            <input type="datetime-local" class="form-control" id="dueDate">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">分配方式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="assignmentType" id="individualAssignment" value="individual" checked>
                                <label class="form-check-label" for="individualAssignment">
                                    个人分配（手动选择人员）
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="assignmentType" id="departmentAssignment" value="department">
                                <label class="form-check-label" for="departmentAssignment">
                                    部门分配（整个部门）
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="assignmentType" id="roleAssignment" value="role">
                                <label class="form-check-label" for="roleAssignment">
                                    角色分配（特定角色）
                                </label>
                            </div>
                        </div>
                        <div class="mb-3" id="departmentSelectDiv" style="display: none;">
                            <label class="form-label">选择部门</label>
                            <select class="form-select" id="departmentSelect">
                                <option value="">请选择部门</option>
                            </select>
                        </div>
                        <div class="mb-3" id="roleSelectDiv" style="display: none;">
                            <label class="form-label">选择角色</label>
                            <select class="form-select" id="roleSelect">
                                <option value="">请选择角色</option>
                                <option value="ADMIN">管理员</option>
                                <option value="QA">质量保证</option>
                                <option value="USER">普通用户</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">备注</label>
                            <textarea class="form-control" id="assignmentNotes" rows="3" placeholder="可选的分配说明"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="proceedToUserSelection()">下一步：选择人员</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/dms/js/auth.js"></script>
    <script>
        let allUsers = [];
        let filteredUsers = [];
        let selectedUsers = new Set();
        let currentPage = 1;
        const usersPerPage = 12;
        let assignmentConfig = {};

        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
            loadCourses();
            loadDepartments();
            setupAssignmentTypeHandlers();
        });

        function setupAssignmentTypeHandlers() {
            document.querySelectorAll('input[name="assignmentType"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    document.getElementById('departmentSelectDiv').style.display = 
                        this.value === 'department' ? 'block' : 'none';
                    document.getElementById('roleSelectDiv').style.display = 
                        this.value === 'role' ? 'block' : 'none';
                });
            });
        }

        async function loadUsers() {
            try {
                console.log('=== 加载用户列表开始 ===');
                const response = await authUtils.secureApiCall('/dms/api/users');
                console.log('用户列表API响应状态:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('用户列表API响应数据:', result);

                    // 处理不同的数据结构
                    let users = [];
                    if (result.success && result.data) {
                        // 如果是标准API响应格式
                        if (Array.isArray(result.data)) {
                            users = result.data;
                        } else if (result.data.content && Array.isArray(result.data.content)) {
                            // 如果是分页数据
                            users = result.data.content;
                        } else {
                            users = result.data;
                        }
                    } else if (Array.isArray(result)) {
                        // 如果直接返回数组
                        users = result;
                    }

                    console.log('处理后的用户数据:', users);
                    console.log('用户数据类型:', typeof users, Array.isArray(users));

                    if (Array.isArray(users)) {
                        allUsers = users;
                        filteredUsers = [...allUsers];
                        displayUsers();
                        updatePagination();
                        console.log('✅ 用户列表加载成功，用户数量:', users.length);
                    } else {
                        console.error('❌ 用户数据不是数组:', users);
                        // 使用模拟数据
                        loadMockUsers();
                    }
                } else {
                    console.error('❌ 用户列表API请求失败:', response.status);
                    loadMockUsers();
                }
            } catch (error) {
                console.error('加载用户列表异常:', error);
                loadMockUsers();
            }
        }

        function loadMockUsers() {
            console.log('使用模拟用户数据');
            allUsers = [
                {
                    id: 1,
                    username: 'admin',
                    firstName: 'System',
                    lastName: 'Administrator',
                    email: '<EMAIL>',
                    departmentName: 'IT部',
                    departmentId: 1,
                    isActive: true,
                    roles: ['ADMIN']
                },
                {
                    id: 2,
                    username: 'qa_user',
                    firstName: 'Quality',
                    lastName: 'Assurance',
                    email: '<EMAIL>',
                    departmentName: '质量保证部',
                    departmentId: 2,
                    isActive: true,
                    roles: ['QA']
                },
                {
                    id: 3,
                    username: 'user',
                    firstName: 'Regular',
                    lastName: 'User',
                    email: '<EMAIL>',
                    departmentName: '生产部',
                    departmentId: 3,
                    isActive: true,
                    roles: ['USER']
                }
            ];
            filteredUsers = [...allUsers];
            displayUsers();
            updatePagination();
        }

        async function loadCourses() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/training-courses');
                if (response.ok) {
                    const result = await response.json();
                    const courses = result.data || [];
                    const courseSelect = document.getElementById('courseSelect');
                    courseSelect.innerHTML = '<option value="">请选择课程</option>';
                    courses.forEach(course => {
                        const option = document.createElement('option');
                        option.value = course.id;
                        option.textContent = course.title;
                        courseSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载课程失败:', error);
            }
        }

        async function loadDepartments() {
            // 模拟部门数据
            const departments = [
                { id: 1, name: '质量保证部' },
                { id: 2, name: '研发部' },
                { id: 3, name: '生产部' },
                { id: 4, name: '法规事务部' },
                { id: 5, name: 'IT部' }
            ];

            const departmentFilter = document.getElementById('departmentFilter');
            const departmentSelect = document.getElementById('departmentSelect');
            
            departments.forEach(dept => {
                const option1 = document.createElement('option');
                option1.value = dept.id;
                option1.textContent = dept.name;
                departmentFilter.appendChild(option1);

                const option2 = document.createElement('option');
                option2.value = dept.id;
                option2.textContent = dept.name;
                departmentSelect.appendChild(option2);
            });
        }

        function displayUsers() {
            const usersList = document.getElementById('usersList');
            const startIndex = (currentPage - 1) * usersPerPage;
            const endIndex = startIndex + usersPerPage;

            console.log('显示用户 - 过滤后用户数量:', filteredUsers.length);
            console.log('显示范围:', startIndex, '-', endIndex);

            if (!Array.isArray(filteredUsers)) {
                console.error('❌ filteredUsers不是数组:', filteredUsers);
                usersList.innerHTML = '<div class="col-12"><div class="alert alert-warning">用户数据格式错误</div></div>';
                return;
            }

            const usersToShow = filteredUsers.slice(startIndex, endIndex);
            console.log('要显示的用户:', usersToShow);

            usersList.innerHTML = '';

            if (usersToShow.length === 0) {
                usersList.innerHTML = '<div class="col-12"><div class="alert alert-info">没有找到用户</div></div>';
                return;
            }

            usersToShow.forEach(user => {
                try {
                    const userCard = createUserCard(user);
                    usersList.appendChild(userCard);
                } catch (error) {
                    console.error('创建用户卡片失败:', error, user);
                }
            });
        }

        function createUserCard(user) {
            const col = document.createElement('div');
            col.className = 'col-md-4 col-lg-3';

            const isSelected = selectedUsers.has(user.id);

            // 安全地获取用户信息
            const firstName = user.firstName || '';
            const lastName = user.lastName || '';
            const username = user.username || 'unknown';
            const departmentName = user.departmentName || user.department?.name || '未分配部门';
            const isActive = user.isActive !== undefined ? user.isActive : true;
            const avatarText = firstName ? firstName.charAt(0) : username.charAt(0);

            col.innerHTML = `
                <div class="user-card ${isSelected ? 'selected' : ''}" onclick="toggleUserSelection(${user.id})">
                    <div class="d-flex align-items-center">
                        <div class="user-avatar me-3">
                            ${avatarText.toUpperCase()}
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${firstName} ${lastName}</h6>
                            <small class="text-muted">${username}</small><br>
                            <small class="text-muted">${departmentName}</small>
                        </div>
                        <div class="text-end">
                            <i class="fas fa-check-circle text-success ${isSelected ? '' : 'd-none'}" id="check-${user.id}"></i>
                            <span class="badge ${isActive ? 'bg-success' : 'bg-secondary'}">${isActive ? '活跃' : '禁用'}</span>
                        </div>
                    </div>
                </div>
            `;

            return col;
        }

        function toggleUserSelection(userId) {
            if (selectedUsers.has(userId)) {
                selectedUsers.delete(userId);
            } else {
                selectedUsers.add(userId);
            }
            
            updateUserCardSelection(userId);
            updateAssignmentSummary();
        }

        function updateUserCardSelection(userId) {
            const userCard = document.querySelector(`[onclick="toggleUserSelection(${userId})"]`);
            const checkIcon = document.getElementById(`check-${userId}`);

            if (userCard && selectedUsers.has(userId)) {
                userCard.classList.add('selected');
                if (checkIcon) checkIcon.classList.remove('d-none');
            } else if (userCard) {
                userCard.classList.remove('selected');
                if (checkIcon) checkIcon.classList.add('d-none');
            }
        }

        function updateAssignmentSummary() {
            const summary = document.getElementById('assignmentSummary');
            const selectedCount = document.getElementById('selectedCount');
            
            selectedCount.textContent = selectedUsers.size;
            
            if (selectedUsers.size > 0 && assignmentConfig.courseId) {
                summary.style.display = 'block';
            } else {
                summary.style.display = 'none';
            }
        }

        function filterUsers() {
            const departmentFilter = document.getElementById('departmentFilter').value;
            const roleFilter = document.getElementById('roleFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const searchInput = document.getElementById('searchInput').value.toLowerCase();

            filteredUsers = allUsers.filter(user => {
                const matchesDepartment = !departmentFilter || user.departmentId == departmentFilter;
                const matchesRole = !roleFilter || (user.roles && user.roles.includes(roleFilter));
                const matchesStatus = !statusFilter || 
                    (statusFilter === 'active' && user.isActive) ||
                    (statusFilter === 'inactive' && !user.isActive);
                const matchesSearch = !searchInput || 
                    user.firstName.toLowerCase().includes(searchInput) ||
                    user.lastName.toLowerCase().includes(searchInput) ||
                    user.username.toLowerCase().includes(searchInput);

                return matchesDepartment && matchesRole && matchesStatus && matchesSearch;
            });

            currentPage = 1;
            displayUsers();
            updatePagination();
        }

        function selectAll() {
            filteredUsers.forEach(user => selectedUsers.add(user.id));
            displayUsers();
            updateAssignmentSummary();
        }

        function selectNone() {
            selectedUsers.clear();
            displayUsers();
            updateAssignmentSummary();
        }

        function selectByDepartment() {
            const departmentId = document.getElementById('departmentFilter').value;
            if (!departmentId) {
                alert('请先选择一个部门');
                return;
            }
            
            filteredUsers.forEach(user => {
                if (user.departmentId == departmentId) {
                    selectedUsers.add(user.id);
                }
            });
            
            displayUsers();
            updateAssignmentSummary();
        }

        function updatePagination() {
            const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
            const pagination = document.getElementById('pagination');
            
            pagination.innerHTML = '';
            
            for (let i = 1; i <= totalPages; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
                pagination.appendChild(li);
            }
        }

        function changePage(page) {
            currentPage = page;
            displayUsers();
            updatePagination();
        }

        function showAssignmentModal() {
            new bootstrap.Modal(document.getElementById('assignmentModal')).show();
        }

        function proceedToUserSelection() {
            const courseId = document.getElementById('courseSelect').value;
            const dueDate = document.getElementById('dueDate').value;
            const assignmentType = document.querySelector('input[name="assignmentType"]:checked').value;
            const notes = document.getElementById('assignmentNotes').value;

            if (!courseId) {
                alert('请选择课程');
                return;
            }

            assignmentConfig = {
                courseId: courseId,
                courseName: document.getElementById('courseSelect').selectedOptions[0].text,
                dueDate: dueDate,
                assignmentType: assignmentType,
                notes: notes
            };

            if (assignmentType === 'department') {
                assignmentConfig.departmentId = document.getElementById('departmentSelect').value;
            } else if (assignmentType === 'role') {
                assignmentConfig.roleName = document.getElementById('roleSelect').value;
            }

            // 更新摘要
            document.getElementById('selectedCourse').textContent = assignmentConfig.courseName;
            document.getElementById('selectedDueDate').textContent = dueDate || '无';

            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('assignmentModal')).hide();

            updateAssignmentSummary();
        }

        async function executeAssignment() {
            if (selectedUsers.size === 0 && assignmentConfig.assignmentType === 'individual') {
                alert('请选择要分配的人员');
                return;
            }

            try {
                let response;
                const requestData = {
                    courseId: assignmentConfig.courseId,
                    dueDate: assignmentConfig.dueDate,
                    notes: assignmentConfig.notes
                };

                if (assignmentConfig.assignmentType === 'individual') {
                    requestData.userIds = Array.from(selectedUsers);
                    response = await authUtils.secureApiCall('/dms/api/training/assignments/assign/batch', {
                        method: 'POST',
                        body: JSON.stringify(requestData)
                    });
                } else if (assignmentConfig.assignmentType === 'department') {
                    requestData.departmentId = assignmentConfig.departmentId;
                    response = await authUtils.secureApiCall('/dms/api/training/assignments/assign/department', {
                        method: 'POST',
                        body: JSON.stringify(requestData)
                    });
                } else if (assignmentConfig.assignmentType === 'role') {
                    requestData.roleName = assignmentConfig.roleName;
                    response = await authUtils.secureApiCall('/dms/api/training/assignments/assign/role', {
                        method: 'POST',
                        body: JSON.stringify(requestData)
                    });
                }

                if (response.ok) {
                    const result = await response.json();
                    alert(`培训分配成功！共分配给 ${result.data.assignedCount} 人`);
                    
                    // 重置状态
                    selectedUsers.clear();
                    assignmentConfig = {};
                    document.getElementById('assignmentSummary').style.display = 'none';
                    displayUsers();
                } else {
                    const error = await response.json();
                    alert('分配失败：' + error.message);
                }
            } catch (error) {
                console.error('执行分配失败:', error);
                alert('分配失败，请稍后重试');
            }
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }
    </script>
</body>
</html>
