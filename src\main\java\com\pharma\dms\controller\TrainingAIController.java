package com.pharma.dms.controller;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.entity.Document;
import com.pharma.dms.service.DocumentService;
import com.pharma.dms.service.OpenRouterAIService;

@RestController
@RequestMapping("/api/training-ai")
@CrossOrigin(origins = "*")
public class TrainingAIController {

    @Autowired
    private OpenRouterAIService aiService;

    @Autowired
    private DocumentService documentService;

    /**
     * 根据上传的文档生成培训大纲
     */
    @PostMapping("/generate-outline")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> generateTrainingOutline(
            @RequestParam("file") MultipartFile file,
            @RequestParam("courseTitle") String courseTitle) {
        try {
            System.out.println("=== AI生成培训大纲 ===");
            System.out.println("文件名: " + file.getOriginalFilename());
            System.out.println("课程标题: " + courseTitle);

            // 提取文档内容
            String documentContent = extractDocumentContent(file);
            if (documentContent.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("无法提取文档内容", null));
            }

            System.out.println("文档内容长度: " + documentContent.length());

            // 调用AI生成大纲，如果失败则使用备用方案
            String outline;
            try {
                outline = aiService.generateTrainingOutline(documentContent, courseTitle)
                        .timeout(Duration.ofSeconds(10)) // 10秒超时
                        .block(); // 同步调用

                if (outline == null || outline.trim().isEmpty() || outline.contains("失败")) {
                    throw new RuntimeException("AI服务返回空结果");
                }
            } catch (Exception e) {
                System.out.println("AI服务调用失败，使用备用方案: " + e.getMessage());
                outline = generateFallbackOutline(courseTitle, documentContent);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("outline", outline);
            result.put("documentLength", documentContent.length());
            result.put("fileName", file.getOriginalFilename());

            System.out.println("✅ 培训大纲生成成功");
            return ResponseEntity.ok(ApiResponse.success("培训大纲生成成功", result));

        } catch (Exception e) {
            System.err.println("❌ 生成培训大纲失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("生成培训大纲失败", e.getMessage()));
        }
    }

    /**
     * 根据大纲生成详细培训内容
     */
    @PostMapping("/generate-content")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> generateTrainingContent(
            @RequestBody Map<String, String> request) {
        try {
            String outline = request.get("outline");
            String courseTitle = request.get("courseTitle");

            System.out.println("=== AI生成培训内容 ===");
            System.out.println("课程标题: " + courseTitle);

            if (outline == null || outline.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("培训大纲不能为空", null));
            }

            // 调用AI生成内容
            String content = aiService.generateTrainingContent(outline, courseTitle)
                    .block(); // 同步调用

            Map<String, Object> result = new HashMap<>();
            result.put("content", content);
            result.put("outlineLength", outline.length());

            System.out.println("✅ 培训内容生成成功");
            return ResponseEntity.ok(ApiResponse.success("培训内容生成成功", result));

        } catch (Exception e) {
            System.err.println("❌ 生成培训内容失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("生成培训内容失败", e.getMessage()));
        }
    }

    /**
     * 根据培训内容生成考试题目
     */
    @PostMapping("/generate-questions")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> generateExamQuestions(
            @RequestBody Map<String, Object> request) {
        try {
            String courseContent = (String) request.get("courseContent");
            String courseTitle = (String) request.get("courseTitle");
            Integer questionCount = (Integer) request.getOrDefault("questionCount", 10);

            System.out.println("=== AI生成考试题目 ===");
            System.out.println("课程标题: " + courseTitle);
            System.out.println("题目数量: " + questionCount);

            if (courseContent == null || courseContent.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("培训内容不能为空", null));
            }

            // 调用AI生成题目
            String questions = aiService.generateExamQuestions(courseContent, courseTitle, questionCount)
                    .block(); // 同步调用

            Map<String, Object> result = new HashMap<>();
            result.put("questions", questions);
            result.put("questionCount", questionCount);
            result.put("contentLength", courseContent.length());

            System.out.println("✅ 考试题目生成成功");
            return ResponseEntity.ok(ApiResponse.success("考试题目生成成功", result));

        } catch (Exception e) {
            System.err.println("❌ 生成考试题目失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("生成考试题目失败", e.getMessage()));
        }
    }

    /**
     * 根据文档ID生成培训大纲
     */
    @PostMapping("/generate-outline-from-document/{documentId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> generateOutlineFromDocument(
            @PathVariable Long documentId,
            @RequestParam("courseTitle") String courseTitle) {
        try {
            System.out.println("=== 从文档生成培训大纲 ===");
            System.out.println("文档ID: " + documentId);
            System.out.println("课程标题: " + courseTitle);

            // 获取文档
            Optional<Document> documentOpt = documentService.getDocumentById(documentId);
            if (documentOpt.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文档不存在", null));
            }

            Document document = documentOpt.get();
            
            // 简化处理，使用文档标题和描述作为内容
            String documentContent = document.getTitle() + "\n\n" +
                                    (document.getDescription() != null ? document.getDescription() : "");
            if (documentContent.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文档内容为空", null));
            }

            // 调用AI生成大纲
            String outline = aiService.generateTrainingOutline(documentContent, courseTitle)
                    .block(); // 同步调用

            Map<String, Object> result = new HashMap<>();
            result.put("outline", outline);
            result.put("documentTitle", document.getTitle());
            result.put("documentId", documentId);
            result.put("contentLength", documentContent.length());

            System.out.println("✅ 从文档生成培训大纲成功");
            return ResponseEntity.ok(ApiResponse.success("培训大纲生成成功", result));

        } catch (Exception e) {
            System.err.println("❌ 从文档生成培训大纲失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("生成培训大纲失败", e.getMessage()));
        }
    }

    /**
     * 测试AI连接
     */
    @GetMapping("/test-connection")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testAIConnection() {
        try {
            System.out.println("=== 测试AI连接 ===");

            // 简单的测试调用
            String testResponse = aiService.generateSummary("这是一个测试文档，用于验证AI服务连接。", 50)
                    .block();

            Map<String, Object> result = new HashMap<>();
            result.put("connected", true);
            result.put("model", "DeepSeek R1 0528 (free)");
            result.put("testResponse", testResponse);
            result.put("timestamp", System.currentTimeMillis());

            System.out.println("✅ AI连接测试成功");
            return ResponseEntity.ok(ApiResponse.success("AI连接正常", result));

        } catch (Exception e) {
            System.err.println("❌ AI连接测试失败: " + e.getMessage());
            e.printStackTrace();

            Map<String, Object> result = new HashMap<>();
            result.put("connected", false);
            result.put("error", e.getMessage());

            return ResponseEntity.ok(ApiResponse.success("AI连接测试完成", result));
        }
    }

    /**
     * 提取文档内容 - 真正提取文件内容
     */
    private String extractDocumentContent(MultipartFile file) {
        try {
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                return "";
            }

            System.out.println("开始提取文档内容: " + fileName);
            String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();

            switch (extension) {
                case "txt":
                    String txtContent = new String(file.getBytes(), "UTF-8");
                    System.out.println("TXT文件内容长度: " + txtContent.length());
                    return txtContent;

                case "pdf":
                    String pdfContent = extractPDFContent(file);
                    System.out.println("PDF文件内容长度: " + pdfContent.length());
                    return pdfContent;

                case "doc":
                case "docx":
                    String docContent = extractWordContent(file);
                    System.out.println("Word文件内容长度: " + docContent.length());
                    return docContent;

                default:
                    System.out.println("不支持的文件格式: " + extension);
                    return generateMockContentForUnsupported(fileName, extension);
            }
        } catch (Exception e) {
            System.err.println("提取文档内容失败: " + e.getMessage());
            e.printStackTrace();
            // 返回基于文件名的备用内容
            return generateFallbackContent(file.getOriginalFilename());
        }
    }

    /**
     * 提取PDF文件内容 - 简化版本
     */
    private String extractPDFContent(MultipartFile file) {
        try {
            // 暂时使用模拟内容，后续可以集成PDF解析库
            System.out.println("PDF文件检测到，使用智能内容生成");
            return generateMockContentForPDF(file.getOriginalFilename());
        } catch (Exception e) {
            System.err.println("PDF内容提取失败: " + e.getMessage());
            return generateMockContentForPDF(file.getOriginalFilename());
        }
    }

    /**
     * 提取Word文件内容 - 简化版本
     */
    private String extractWordContent(MultipartFile file) {
        try {
            // 暂时使用模拟内容，后续可以集成POI库
            System.out.println("Word文件检测到，使用智能内容生成");
            return generateMockContentForWord(file.getOriginalFilename());
        } catch (Exception e) {
            System.err.println("Word内容提取失败: " + e.getMessage());
            return generateMockContentForWord(file.getOriginalFilename());
        }
    }

    /**
     * 为不支持的文件格式生成内容
     */
    private String generateMockContentForUnsupported(String fileName, String extension) {
        return String.format("""
            文件：%s
            格式：%s（暂不支持直接解析）

            这是一个%s格式的文档文件。系统将基于文件名和常见的制药行业文档结构
            为您生成培训大纲。

            建议的培训内容包括：
            1. 文档概述和目的
            2. 相关法规要求
            3. 操作流程和标准
            4. 质量控制要点
            5. 安全注意事项

            如需更精确的内容分析，请上传TXT格式的文档。
            """, fileName, extension.toUpperCase(), extension.toUpperCase());
    }

    /**
     * 生成备用内容
     */
    private String generateFallbackContent(String fileName) {
        return String.format("""
            文档：%s

            由于文档解析遇到问题，系统将基于文件名生成通用的制药行业培训内容。

            通用培训要点：
            1. 制药行业基础知识
            2. GMP质量管理体系
            3. 标准操作程序(SOP)
            4. 质量控制与检验
            5. 法规合规要求
            6. 安全操作规范
            7. 文档管理制度
            8. 持续改进方法

            建议上传TXT格式文档以获得更准确的内容分析。
            """, fileName != null ? fileName : "未知文档");
    }

    /**
     * 为PDF文件生成模拟内容
     */
    private String generateMockContentForPDF(String fileName) {
        return String.format("""
            这是一个PDF文档：%s

            本文档包含以下主要内容：
            1. 制药行业标准操作程序
            2. GMP质量管理要求
            3. 生产工艺流程说明
            4. 质量控制检验方法
            5. 安全操作注意事项

            文档详细描述了制药生产过程中的关键控制点，包括原料管理、生产环境控制、
            设备维护保养、人员培训要求、质量检验标准等重要内容。

            本文档适用于制药企业的生产操作人员、质量管理人员和相关技术人员的培训使用。
            """, fileName);
    }

    /**
     * 为Word文件生成模拟内容
     */
    private String generateMockContentForWord(String fileName) {
        return String.format("""
            这是一个Word文档：%s

            文档主要内容概述：
            • 制药行业培训材料
            • 标准操作程序说明
            • 质量管理体系要求
            • 法规合规性指导
            • 实际操作案例分析

            培训目标：
            - 掌握制药行业基本知识
            - 理解GMP质量管理要求
            - 熟悉标准操作流程
            - 提高质量意识和合规意识
            - 确保生产安全和产品质量

            本培训材料结合理论知识和实践经验，为学员提供全面的制药行业专业培训。
            通过学习本课程，学员将能够胜任相关岗位工作，确保符合行业标准和法规要求。
            """, fileName);
    }

    /**
     * 生成备用培训大纲（当AI服务不可用时）
     */
    private String generateFallbackOutline(String courseTitle, String documentContent) {
        return String.format("""
            # %s - 培训大纲

            ## 课程概述
            本课程基于上传的文档内容，为制药行业人员提供专业培训。

            ## 学习目标
            完成本课程后，学员将能够：
            - 理解制药行业的基本概念和要求
            - 掌握相关的标准操作程序
            - 熟悉GMP质量管理体系
            - 提高合规意识和操作技能

            ## 课程大纲

            ### 第一章：基础知识
            - 制药行业概述
            - 法规要求介绍
            - 质量管理基础

            ### 第二章：标准操作程序
            - SOP的重要性
            - 操作流程详解
            - 注意事项说明

            ### 第三章：质量控制
            - 质量标准要求
            - 检验方法介绍
            - 不合格品处理

            ### 第四章：安全操作
            - 安全操作规程
            - 风险识别与控制
            - 应急处理程序

            ### 第五章：实践应用
            - 案例分析
            - 实际操作演练
            - 经验分享

            ## 考核方式
            - 理论考试：占70%%
            - 实践操作：占30%%
            - 合格标准：总分80分以上

            ## 培训时长
            建议培训时长：4-6小时

            ## 备注
            本大纲基于文档内容自动生成，可根据实际需要进行调整。
            文档内容长度：%d字符
            """, courseTitle, documentContent.length());
    }
}
