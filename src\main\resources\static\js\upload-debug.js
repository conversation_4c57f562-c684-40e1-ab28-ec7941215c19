/**
 * 文档上传调试工具
 * 专门用于调试文档上传功能的问题
 */

class UploadDebugger {
    constructor() {
        this.debugMode = true;
        this.uploadAttempts = [];
        this.init();
    }

    init() {
        console.log('🔧 文档上传调试器已初始化');
        this.addDebugPanel();
        this.interceptFormSubmission();
    }

    addDebugPanel() {
        // 创建调试面板
        const debugPanel = document.createElement('div');
        debugPanel.id = 'upload-debug-panel';
        debugPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 400px;
            max-height: 500px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            font-family: monospace;
            font-size: 12px;
            overflow-y: auto;
            display: none;
        `;

        debugPanel.innerHTML = `
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                <h6 style="margin: 0; color: #007bff;">📤 上传调试器</h6>
                <button onclick="uploadDebugger.togglePanel()" style="background: none; border: none; font-size: 16px;">❌</button>
            </div>
            <div id="debug-content">
                <div style="margin-bottom: 10px;">
                    <button onclick="uploadDebugger.testConnection()" class="btn btn-sm btn-primary">测试连接</button>
                    <button onclick="uploadDebugger.showUploadModal()" class="btn btn-sm btn-success">上传文档</button>
                    <button onclick="uploadDebugger.clearLogs()" class="btn btn-sm btn-secondary">清除日志</button>
                </div>
                <div id="debug-logs" style="background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">
                    <div style="color: #666;">上传调试器已就绪，可以开始测试上传功能...</div>
                </div>
            </div>
        `;

        document.body.appendChild(debugPanel);

        // 添加切换按钮
        const toggleBtn = document.createElement('button');
        toggleBtn.innerHTML = '🔧';
        toggleBtn.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 40px;
            height: 40px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 16px;
            cursor: pointer;
            z-index: 10000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;
        toggleBtn.onclick = () => this.togglePanel();
        document.body.appendChild(toggleBtn);
    }

    togglePanel() {
        const panel = document.getElementById('upload-debug-panel');
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logContainer = document.getElementById('debug-logs');
        
        const colors = {
            info: '#333',
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107'
        };

        const logEntry = document.createElement('div');
        logEntry.style.cssText = `
            margin-bottom: 5px;
            padding: 5px;
            border-left: 3px solid ${colors[type]};
            background: ${type === 'error' ? '#fff5f5' : type === 'success' ? '#f0fff4' : '#f8f9fa'};
        `;
        logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> <span style="color: ${colors[type]};">${message}</span>`;
        
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    clearLogs() {
        document.getElementById('debug-logs').innerHTML = '<div style="color: #666;">日志已清除...</div>';
    }

    showUploadModal() {
        this.log('🔧 打开上传文档模态框...');
        try {
            const modal = new bootstrap.Modal(document.getElementById('uploadDocumentModal'));
            modal.show();
            this.log('✅ 上传模态框已打开');
        } catch (error) {
            this.log('❌ 打开上传模态框失败: ' + error.message);
        }
    }

    interceptFormSubmission() {
        // 拦截原有的表单提交
        const originalForm = document.getElementById('uploadDocumentForm');
        if (originalForm) {
            this.log('🔧 找到上传表单，开始拦截...', 'info');

            // 移除原有的事件监听器
            const newForm = originalForm.cloneNode(true);
            originalForm.parentNode.replaceChild(newForm, originalForm);

            // 添加新的调试版本
            newForm.addEventListener('submit', (e) => this.handleUpload(e));
            this.log('✅ 表单拦截设置完成', 'success');
        } else {
            this.log('⚠️ 未找到上传表单，将在模态框打开时重试', 'warning');

            // 监听模态框打开事件
            const modal = document.getElementById('uploadDocumentModal');
            if (modal) {
                modal.addEventListener('shown.bs.modal', () => {
                    setTimeout(() => {
                        this.interceptFormSubmission();
                    }, 100);
                });
            }
        }
    }

    async handleUpload(e) {
        e.preventDefault();

        const attemptId = Date.now();
        this.log(`🚀 开始上传尝试 #${attemptId}`, 'info');
        console.log('🔧 上传调试: 表单提交事件被触发', e);
        
        const form = e.target;
        const formData = new FormData(form);
        
        try {
            // 1. 验证表单数据
            this.log('📋 验证表单数据...', 'info');
            console.log('🔧 上传调试: 开始验证表单数据', formData);
            const validation = this.validateForm(formData);
            console.log('🔧 上传调试: 表单验证结果', validation);
            if (!validation.valid) {
                this.log(`❌ 表单验证失败: ${validation.error}`, 'error');
                return;
            }
            this.log('✅ 表单验证通过', 'success');

            // 2. 检查认证状态
            this.log('🔐 检查认证状态...', 'info');
            const authCheck = this.checkAuth();
            if (!authCheck.valid) {
                this.log(`❌ 认证检查失败: ${authCheck.error}`, 'error');
                return;
            }
            this.log(`✅ 认证检查通过: ${authCheck.username}`, 'success');

            // 3. 准备请求
            this.log('📦 准备上传请求...', 'info');
            const requestDetails = this.prepareRequest(formData);
            this.log(`📊 请求详情: ${JSON.stringify(requestDetails, null, 2)}`, 'info');

            // 4. 发送请求
            this.log('🌐 发送上传请求...', 'info');
            const response = await this.sendUploadRequest(formData, authCheck.token);
            
            // 5. 处理响应
            this.log(`📨 收到响应: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
            
            if (response.ok) {
                const result = await response.json();
                this.log(`✅ 上传成功: ${JSON.stringify(result, null, 2)}`, 'success');
                
                // 关闭模态框并重新加载
                this.closeModalAndReload(form);
            } else {
                const errorText = await response.text();
                this.log(`❌ 上传失败: ${errorText}`, 'error');
                alert('上传失败: ' + errorText);
            }

        } catch (error) {
            this.log(`💥 上传异常: ${error.message}`, 'error');
            this.log(`📍 错误堆栈: ${error.stack}`, 'error');
            alert('上传失败: ' + error.message);
        }
    }

    validateForm(formData) {
        const file = formData.get('file');
        const title = formData.get('title');

        if (!file || file.size === 0) {
            return { valid: false, error: '未选择文件' };
        }

        if (!title || title.trim() === '') {
            return { valid: false, error: '标题不能为空' };
        }

        if (file.size > 50 * 1024 * 1024) {
            return { valid: false, error: '文件大小超过50MB限制' };
        }

        return { 
            valid: true, 
            details: {
                fileName: file.name,
                fileSize: file.size,
                fileType: file.type,
                title: title
            }
        };
    }

    checkAuth() {
        const token = localStorage.getItem('token');
        const user = localStorage.getItem('user');

        if (!token) {
            return { valid: false, error: '未找到认证令牌' };
        }

        if (!user) {
            return { valid: false, error: '未找到用户信息' };
        }

        try {
            const userObj = JSON.parse(user);
            return { 
                valid: true, 
                token: token,
                username: userObj.username || '未知用户'
            };
        } catch (e) {
            return { valid: false, error: '用户信息解析失败' };
        }
    }

    prepareRequest(formData) {
        const details = {};
        for (let [key, value] of formData.entries()) {
            if (value instanceof File) {
                details[key] = {
                    name: value.name,
                    size: value.size,
                    type: value.type
                };
            } else {
                details[key] = value;
            }
        }
        return details;
    }

    async sendUploadRequest(formData, token) {
        const startTime = Date.now();
        
        const response = await fetch('/dms/api/documents/upload', {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + token
                // 不设置Content-Type，让浏览器自动设置
            },
            body: formData
        });

        const endTime = Date.now();
        this.log(`⏱️ 请求耗时: ${endTime - startTime}ms`, 'info');

        return response;
    }

    closeModalAndReload(form) {
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('uploadDocumentModal'));
        if (modal) {
            modal.hide();
        }

        // 重置表单
        form.reset();

        // 重新加载文档列表
        setTimeout(() => {
            this.log('🔄 重新加载文档列表...', 'info');
            if (typeof loadDocuments === 'function') {
                loadDocuments();
            } else if (typeof documentManager !== 'undefined') {
                documentManager.loadDocuments();
            }
        }, 500);
    }

    async testConnection() {
        this.log('🔗 测试API连接...', 'info');
        
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                this.log('❌ 未找到认证令牌', 'error');
                return;
            }

            const response = await fetch('/dms/api/documents?page=0&size=1', {
                headers: {
                    'Authorization': 'Bearer ' + token
                }
            });

            if (response.ok) {
                this.log('✅ API连接正常', 'success');
                const result = await response.json();
                this.log(`📊 API响应: ${JSON.stringify(result, null, 2)}`, 'info');
            } else {
                this.log(`❌ API连接失败: ${response.status} ${response.statusText}`, 'error');
            }
        } catch (error) {
            this.log(`💥 连接测试异常: ${error.message}`, 'error');
        }
    }
}

// 页面加载完成后初始化调试器
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他脚本已加载
    setTimeout(() => {
        window.uploadDebugger = new UploadDebugger();
        console.log('🔧 文档上传调试器已就绪');
    }, 1000);
});
