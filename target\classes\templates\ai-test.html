<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI功能测试 - 制药文档管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-card {
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
        }
        .test-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
        .result-container {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-primary">AI测试中心</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard">
                                <i class="fas fa-arrow-left me-2"></i>返回主页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="#connection-test">
                                <i class="fas fa-plug me-2"></i>连接测试
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#document-analysis">
                                <i class="fas fa-file-alt me-2"></i>文档分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#training-generation">
                                <i class="fas fa-graduation-cap me-2"></i>培训生成
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">AI功能深度测试</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="clearAllLogs()">
                            <i class="fas fa-trash me-1"></i>清除日志
                        </button>
                    </div>
                </div>

                <!-- 连接测试 -->
                <section id="connection-test" class="mb-5">
                    <div class="card test-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-plug me-2"></i>AI连接测试
                                <span id="connection-status" class="status-indicator status-info"></span>
                                <span id="connection-text">未测试</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <button class="btn btn-primary" onclick="testAIConnection()">
                                        <i class="fas fa-play me-2"></i>开始连接测试
                                    </button>
                                    <button class="btn btn-outline-secondary ms-2" onclick="clearConnectionLog()">
                                        <i class="fas fa-eraser me-1"></i>清除日志
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <div class="text-end">
                                        <small class="text-muted">模型: DeepSeek R1 0528 (free)</small>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="log-container p-3" id="connection-log">
                                    <div class="text-muted">点击"开始连接测试"查看详细日志...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 文档分析测试 -->
                <section id="document-analysis" class="mb-5">
                    <div class="card test-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-file-alt me-2"></i>文档分析测试
                                <span id="analysis-status" class="status-indicator status-info"></span>
                                <span id="analysis-text">未测试</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="analysisForm" enctype="multipart/form-data">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="analysisFile" class="form-label">选择测试文档</label>
                                            <input type="file" class="form-control" id="analysisFile" 
                                                   accept=".txt,.pdf,.doc,.docx" required>
                                            <div class="form-text">支持 TXT, PDF, DOC, DOCX 格式</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="analysisType" class="form-label">分析类型</label>
                                            <select class="form-select" id="analysisType">
                                                <option value="summary">文档摘要</option>
                                                <option value="categorization">文档分类</option>
                                                <option value="compliance">合规检查</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-search me-2"></i>开始分析
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="clearAnalysisLog()">
                                        <i class="fas fa-eraser me-1"></i>清除日志
                                    </button>
                                </div>
                            </form>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>分析日志</h6>
                                    <div class="log-container p-3" id="analysis-log">
                                        <div class="text-muted">选择文档开始分析...</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>分析结果</h6>
                                    <div class="result-container p-3" id="analysis-result">
                                        <div class="text-muted">分析结果将显示在这里...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 培训生成测试 -->
                <section id="training-generation" class="mb-5">
                    <div class="card test-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-graduation-cap me-2"></i>培训内容生成测试
                                <span id="training-status" class="status-indicator status-info"></span>
                                <span id="training-text">未测试</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="trainingForm" enctype="multipart/form-data">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="trainingFile" class="form-label">选择培训文档</label>
                                            <input type="file" class="form-control" id="trainingFile" 
                                                   accept=".txt,.pdf,.doc,.docx" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="courseTitle" class="form-label">课程标题</label>
                                            <input type="text" class="form-control" id="courseTitle" 
                                                   placeholder="例如：GMP质量管理培训" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-magic me-2"></i>生成培训大纲
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="clearTrainingLog()">
                                        <i class="fas fa-eraser me-1"></i>清除日志
                                    </button>
                                </div>
                            </form>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>生成日志</h6>
                                    <div class="log-container p-3" id="training-log">
                                        <div class="text-muted">上传文档开始生成...</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>生成结果</h6>
                                    <div class="result-container p-3" id="training-result">
                                        <div class="text-muted">培训大纲将显示在这里...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 测试总结 -->
                <section id="test-summary" class="mb-5">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>测试总结
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h3" id="total-tests">0</div>
                                        <div class="text-muted">总测试数</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h3 text-success" id="success-tests">0</div>
                                        <div class="text-muted">成功测试</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h3 text-danger" id="failed-tests">0</div>
                                        <div class="text-muted">失败测试</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/dms/js/auth.js"></script>
    <script>
        // 测试统计
        let testStats = {
            total: 0,
            success: 0,
            failed: 0
        };

        // 更新测试统计
        function updateTestStats() {
            document.getElementById('total-tests').textContent = testStats.total;
            document.getElementById('success-tests').textContent = testStats.success;
            document.getElementById('failed-tests').textContent = testStats.failed;
        }

        // 更新状态指示器
        function updateStatus(type, status, text) {
            const indicator = document.getElementById(`${type}-status`);
            const textElement = document.getElementById(`${type}-text`);
            
            indicator.className = `status-indicator status-${status}`;
            textElement.textContent = text;
        }

        // 添加日志
        function addLog(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry text-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'dark'}`;
            logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
        }

        // 清除日志
        function clearLog(containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = '<div class="text-muted">日志已清除...</div>';
        }

        function clearConnectionLog() { clearLog('connection-log'); }
        function clearAnalysisLog() { clearLog('analysis-log'); }
        function clearTrainingLog() { clearLog('training-log'); }
        function clearAllLogs() {
            clearConnectionLog();
            clearAnalysisLog();
            clearTrainingLog();
        }

        // AI连接测试
        async function testAIConnection() {
            testStats.total++;
            updateStatus('connection', 'warning', '测试中...');
            addLog('connection-log', '开始AI连接测试...', 'info');
            
            try {
                const response = await authUtils.secureApiCall('/dms/api/training-ai/test-connection');
                
                if (response.ok) {
                    const result = await response.json();
                    addLog('connection-log', `API响应状态: ${response.status}`, 'success');
                    addLog('connection-log', `响应数据: ${JSON.stringify(result, null, 2)}`, 'info');
                    
                    if (result.success && result.data.connected) {
                        testStats.success++;
                        updateStatus('connection', 'success', '连接成功');
                        addLog('connection-log', '✅ AI连接测试成功!', 'success');
                        addLog('connection-log', `模型: ${result.data.model}`, 'info');
                        addLog('connection-log', `测试响应: ${result.data.testResponse}`, 'info');
                    } else {
                        testStats.failed++;
                        updateStatus('connection', 'error', '连接失败');
                        addLog('connection-log', '❌ AI连接测试失败', 'error');
                        addLog('connection-log', `错误: ${result.data.error || '未知错误'}`, 'error');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                testStats.failed++;
                updateStatus('connection', 'error', '连接异常');
                addLog('connection-log', `❌ 连接测试异常: ${error.message}`, 'error');
            }
            
            updateTestStats();
        }

        // 文档分析表单提交
        document.getElementById('analysisForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            testStats.total++;
            updateStatus('analysis', 'warning', '分析中...');
            addLog('analysis-log', '开始文档分析测试...', 'info');
            
            const formData = new FormData();
            const file = document.getElementById('analysisFile').files[0];
            const analysisType = document.getElementById('analysisType').value;
            
            formData.append('file', file);
            formData.append('documentType', 'SOP');
            
            try {
                addLog('analysis-log', `文件: ${file.name} (${(file.size/1024).toFixed(1)}KB)`, 'info');
                addLog('analysis-log', `分析类型: ${analysisType}`, 'info');
                
                let endpoint;
                switch(analysisType) {
                    case 'summary':
                        endpoint = '/dms/api/ai/analyze-document';
                        break;
                    case 'categorization':
                        endpoint = '/dms/api/ai/categorize-document';
                        break;
                    case 'compliance':
                        endpoint = '/dms/api/ai/check-compliance';
                        break;
                }
                
                const response = await authUtils.secureApiCall(endpoint, {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    testStats.success++;
                    updateStatus('analysis', 'success', '分析完成');
                    addLog('analysis-log', '✅ 文档分析成功!', 'success');
                    
                    // 显示结果
                    const resultContainer = document.getElementById('analysis-result');
                    resultContainer.innerHTML = `<pre>${JSON.stringify(result.data, null, 2)}</pre>`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                testStats.failed++;
                updateStatus('analysis', 'error', '分析失败');
                addLog('analysis-log', `❌ 分析失败: ${error.message}`, 'error');
            }
            
            updateTestStats();
        });

        // 培训生成表单提交
        document.getElementById('trainingForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            testStats.total++;
            updateStatus('training', 'warning', '生成中...');
            addLog('training-log', '开始培训大纲生成测试...', 'info');
            
            const formData = new FormData();
            const file = document.getElementById('trainingFile').files[0];
            const courseTitle = document.getElementById('courseTitle').value;
            
            formData.append('file', file);
            formData.append('courseTitle', courseTitle);
            
            try {
                addLog('training-log', `文件: ${file.name} (${(file.size/1024).toFixed(1)}KB)`, 'info');
                addLog('training-log', `课程标题: ${courseTitle}`, 'info');
                
                const response = await authUtils.secureApiCall('/dms/api/training-ai/generate-outline', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    testStats.success++;
                    updateStatus('training', 'success', '生成完成');
                    addLog('training-log', '✅ 培训大纲生成成功!', 'success');
                    addLog('training-log', `文档长度: ${result.data.documentLength} 字符`, 'info');
                    
                    // 显示结果
                    const resultContainer = document.getElementById('training-result');
                    resultContainer.innerHTML = `<div class="markdown-content">${result.data.outline.replace(/\n/g, '<br>')}</div>`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                testStats.failed++;
                updateStatus('training', 'error', '生成失败');
                addLog('training-log', `❌ 生成失败: ${error.message}`, 'error');
            }
            
            updateTestStats();
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查认证状态
            if (typeof authUtils !== 'undefined') {
                authUtils.initAuth();
                if (!authUtils.isAuthenticated()) {
                    console.log('用户未认证，跳转到登录页面');
                    window.location.href = '/dms/login';
                    return;
                }
                console.log('✅ 用户已认证，初始化AI测试页面');
            } else {
                console.warn('⚠️ authUtils未加载，继续初始化页面');
            }

            updateTestStats();
            console.log('🧪 AI测试页面已加载');
        });
    </script>
</body>
</html>
