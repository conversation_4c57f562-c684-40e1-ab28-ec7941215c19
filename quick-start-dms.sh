#!/bin/bash

# 制药DMS系统 - 快速启动脚本

echo "🚀 快速启动制药DMS系统..."

# 切换到项目目录
cd /d/learn/java/dms

# 清理进程
echo "🔄 清理旧进程..."
taskkill.exe //F //IM java.exe 2>/dev/null || true
taskkill.exe //F //IM postgres.exe 2>/dev/null || true
sleep 2

# 启动PostgreSQL
echo "🗄️  启动PostgreSQL..."
/d/sql/pgsql/bin/pg_ctl -D /d/sql/pgsql/data start

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 8

# 检查PostgreSQL状态
if tasklist.exe | grep -q postgres.exe; then
    echo "✅ PostgreSQL启动成功，使用PostgreSQL数据库"
    PROFILE="postgresql"
else
    echo "⚠️  PostgreSQL启动失败，使用H2数据库"
    PROFILE="h2"
fi

# 启动应用程序
echo "🚀 启动DMS应用程序 (配置: $PROFILE)..."
echo "📱 访问地址: http://172.100.15.120:8081/dms/login"
echo "👤 默认用户: admin / admin123"
echo ""

mvn spring-boot:run "-Dspring-boot.run.profiles=$PROFILE"
